import {model, property, belongsTo} from '@loopback/repository';
import {UserModifiableEntity} from '@sourceloop/core';
import {
  ProductVariant,
  ProductVariantWithRelations,
} from './product-variant.model';
import {Warehouse} from '../auth-service';

@model({settings: {strict: false}, name: 'inventory_items'})
export class InventoryItem extends UserModifiableEntity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
    required: true,
  })
  sku: string;

  @property({
    type: 'string',
    required: true,
    name: 'warehouse_id',
  })
  warehouseId: string;

  @property({
    type: 'number',
    required: true,
    name: 'stock_on_hand',
  })
  stockOnHand: number;

  @property({
    type: 'number',
    required: true,
    name: 'stock_allocated',
  })
  stockAllocated: number;

  @property({
    type: 'number',
    name: 'reorder_threshold',
  })
  reorderThreshold: number;

  @belongsTo(
    () => ProductVariant,
    {keyTo: 'id'},
    {name: 'product_variant_id', required: true},
  )
  productVariantId: string;

  constructor(data?: Partial<InventoryItem>) {
    super(data);
  }
}

export interface InventoryItemRelations {
  warehouse?: Warehouse;
  productVariant?: ProductVariantWithRelations;
}

export type InventoryItemWithRelations = InventoryItem & InventoryItemRelations;
