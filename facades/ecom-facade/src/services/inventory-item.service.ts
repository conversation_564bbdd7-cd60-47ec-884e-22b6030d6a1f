import {injectable, BindingScope} from '@loopback/core';
import {Filter} from '@loopback/repository';
import {InventoryItem, InventoryItemWithRelations, Warehouse} from '../models';
import {AuthUser} from '@sourceloop/authentication-service';
import {ModifiedRestService, restService} from '@sourceloop/core';
import {AuthProxyType} from '../datasources/configs';

@injectable({scope: BindingScope.TRANSIENT})
export class InventoryItemService {
  constructor(
    @restService(AuthUser)
    private readonly authProvider: AuthProxyType,
    @restService(Warehouse)
    private readonly warehouseService: ModifiedRestService<Warehouse>,
  ) {}

  async applySellerFilter(
    xOrigin: string,
    token: string | undefined,
    filter?: Filter<InventoryItem>,
  ): Promise<Filter<InventoryItem>> {
    if (xOrigin === 'ecomdukes-seller') {
      const result = await this.authProvider.getMe(token ?? '', xOrigin);
      filter = filter ?? {};

      filter.where = filter.where
        ? {
            ...filter.where,
            createdBy: result.userTenantId,
          }
        : {createdBy: result.userTenantId};
    }
    return filter ?? {};
  }

  async findWithWarehouse(
    inventoryItems: InventoryItem[],
  ): Promise<InventoryItemWithRelations[]> {
    const warehouseIds = Array.from(
      new Set(
        inventoryItems
          .map(item => item.warehouseId)
          .filter((id): id is string => typeof id === 'string'),
      ),
    );

    let warehouseMap = new Map<string, Warehouse>();

    if (warehouseIds.length > 0) {
      const warehouses: Warehouse[] = await this.warehouseService.find({
        where: {id: {inq: warehouseIds}},
      });

      warehouseMap = new Map<string, Warehouse>(
        warehouses
          .filter(w => typeof w.id === 'string')
          .map(w => [w.id as string, w]),
      );
    }

    const enrichedItems = inventoryItems.map(item => ({
      ...item,
      warehouse: item.warehouseId
        ? warehouseMap.get(item.warehouseId)
        : undefined,
    }));

    const fullyEnrichedItems =
      await this.enrichWithFeaturedAssetPreviewInventoryItem(
        enrichedItems as InventoryItemWithRelations[],
      );

    return fullyEnrichedItems as InventoryItemWithRelations[];
  }

  async enrichWithFeaturedAssetPreviewInventoryItem(
    inventoryItems: InventoryItem[],
  ): Promise<InventoryItem[]> {
    const cdnOrigin = process.env.CDN_ORIGIN ?? '';

    for (const item of inventoryItems) {
      const productVariant = (item as any).productVariant;
      const featuredAsset = productVariant?.featuredAsset;

      if (featuredAsset?.preview && cdnOrigin) {
        featuredAsset.previewUrl = `${cdnOrigin}/${featuredAsset.preview}`;
      }
    }

    return inventoryItems;
  }
}
