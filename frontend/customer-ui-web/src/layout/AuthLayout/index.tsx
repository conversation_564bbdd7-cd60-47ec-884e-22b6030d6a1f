'use client';

import { Box } from '@mui/material';
import { HEADER_HEIGHT } from 'config';
import { lazy, ReactNode } from 'react';

// next

// material-ui
const Header = lazy(() => import('./Header'));

// ==============================|| LAYOUTS - STRUCTURE ||============================== //

interface Props {
  children: ReactNode;
}

export default function AuthLayout({ children }: Props) {
  return (
    <>
      <Header />
      <Box sx={{ marginTop: `${HEADER_HEIGHT}px` }}>{children}</Box>
    </>
  );
}
