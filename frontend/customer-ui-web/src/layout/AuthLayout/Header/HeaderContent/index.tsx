import Box from '@mui/material/Box';

// project-imports

import DrawerHeader from 'layout/DashboardLayout/Drawer/DrawerHeader';
import {HEADER_HEIGHT} from 'config';

// ==============================|| HEADER - CONTENT ||============================== //

export default function HeaderContent() {
  return (
    <Box
      sx={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        width: '100%',
        zIndex: 1100,
        bgcolor: 'white',
        boxShadow: '0px 4px 10px rgba(0, 0, 0, 0.1)',
        // padding: '2px 20px',
        marginBottom: `${HEADER_HEIGHT}px`,
      }}
    >
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          width: '100%',
        }}
      >
        <DrawerHeader open={true} />
      </Box>
    </Box>
  );
}
