// 'use client';

// import {Avatar, Box, Tooltip, Typography} from '@mui/material';
// import {CardStyle} from 'enums/pageSection.enum';
// import {useRouter} from 'next/navigation';
// import {SectionItem} from 'types/page-section';

// type Props = {
//   item: SectionItem;
//   cardStyle?: CardStyle;
// };

// const CollectionCard = ({item, cardStyle}: Props) => {
//   const router = useRouter();

//   const handleClick = () => {
//     router.push(`listing?collectionIds=${item.entityId}`);
//   };

//   const title = item.collection?.name || item.title || '';
//   const subtitle = item.subtitle || '';
//   const thumbnail = item.previewUrl || '';

//   if (cardStyle === CardStyle.AVATAR_WITH_TITLE) {
//     return (
//       <Box
//         onClick={handleClick}
//         sx={{
//           display: 'flex',
//           flexDirection: 'column',
//           alignItems: 'center',
//           justifyContent: 'center',
//           cursor: 'pointer',
//           textAlign: 'center',
//           width: 100,
//         }}
//       >
//         <Avatar
//           src={thumbnail}
//           alt={title}
//           sx={{width: 64, height: 64, mb: 1, bgcolor: 'white'}}
//         />
//         <Tooltip title={title} arrow>
//           <Typography
//             variant="subtitle2"
//             fontWeight={400}
//             sx={{
//               fontSize: '0.85rem',
//               maxWidth: '100%',
//               whiteSpace: 'nowrap',
//               overflow: 'hidden',
//               textOverflow: 'ellipsis',
//             }}
//           >
//             {title}
//           </Typography>
//         </Tooltip>
//       </Box>
//     );
//   }

//   return (
//     <Box
//       onClick={handleClick}
//       sx={{
//         width: 200,
//         flex: '0 0 auto',
//         borderRadius: '16px',
//         overflow: 'hidden',
//         backgroundColor: '#fff',
//         cursor: 'pointer',
//         position: 'relative',
//         boxShadow: 1,
//         '&:hover': {
//           transform: 'scale(1.02)',
//         },
//       }}
//     >
//       {cardStyle === CardStyle.IMAGE_TITLE_SUBTITLE ? (
//         <Box sx={{height: 230, display: 'flex', flexDirection: 'column'}}>
//           <Box
//             sx={{
//               height: '40%',
//               backgroundColor: '#C8D3D5',
//               p: 1.5,
//               display: 'flex',
//               flexDirection: 'column',
//               justifyContent: 'center',
//             }}
//           >
//             <Typography variant="h6" fontWeight={700} sx={{color: '#0E1C1F'}}>
//               {title}
//             </Typography>
//             {subtitle && (
//               <Typography variant="caption" sx={{color: '#0E1C1F', mt: 0.5}}>
//                 {subtitle}
//               </Typography>
//             )}
//           </Box>
//           <Box
//             sx={{
//               height: '100%',
//               backgroundImage: `url(${thumbnail})`,
//               backgroundSize: 'cover',
//               backgroundPosition: 'center',
//               flexGrow: 1,
//             }}
//           />
//         </Box>
//       ) : cardStyle === CardStyle.IMAGE_TITLE ? (
//         <Box
//           sx={{
//             height: 230,
//             backgroundImage: `url(${thumbnail})`,
//             backgroundSize: 'cover',
//             backgroundPosition: 'center',
//             position: 'relative',
//             display: 'flex',
//             alignItems: 'flex-start',
//             justifyContent: 'center',
//             p: 2,
//           }}
//         >
//           <Typography
//             variant="subtitle1"
//             fontWeight={600}
//             sx={{color: '#FFF', textShadow: '0 0 5px rgba(0,0,0,0.6)'}}
//           >
//             {title}
//           </Typography>
//         </Box>
//       ) : (
//         <Box
//           sx={{
//             height: 230,
//             backgroundImage: `url(${thumbnail})`,
//             backgroundSize: 'cover',
//             backgroundPosition: 'center',
//           }}
//         />
//       )}
//     </Box>
//   );
// };

// export default CollectionCard;
// 'use client';

// import {useState} from 'react';
// import {
//   Avatar,
//   Box,
//   Tooltip,
//   Typography,
//   IconButton,
//   Collapse,
//   List,
//   ListItemButton,
//   ListItemText,
// } from '@mui/material';
// import {ExpandMore, ExpandLess} from '@mui/icons-material';
// import {CardStyle} from 'enums/pageSection.enum';
// import {useRouter} from 'next/navigation';
// import {SectionItem} from 'types/page-section';
// import {useLazyGetCollectionsQuery} from 'redux/ecom/collectionApiSlice';

// type Props = {
//   item: SectionItem;
//   cardStyle?: CardStyle;
// };

// const CollectionCard = ({item, cardStyle}: Props) => {
//   const router = useRouter();
//   const [open, setOpen] = useState(false);
//   const [children, setChildren] = useState<any[]>([]);
//   const [triggerGetCollections, {isFetching}] = useLazyGetCollectionsQuery();

//   const handleClick = () => {
//     router.push(`listing?collectionIds=${item.entityId}`);
//   };

//   const fetchChildren = async () => {
//     try {
//       const collections = await triggerGetCollections({
//         where: {parentId: item.entityId},
//       }).unwrap();

//       // `collections` is already an array
//       setChildren(collections || []);
//     } catch (err) {
//       console.error('Failed to load childrens', err);
//     }
//   };

//   const toggleChildren = () => {
//     if (!open && children.length === 0) {
//       fetchChildren();
//     }
//     setOpen(prev => !prev);
//   };

//   const title = item.collection?.name || item.title || '';
//   const subtitle = item.subtitle || '';
//   const thumbnail = item.previewUrl || '';

//   if (cardStyle === CardStyle.AVATAR_WITH_TITLE) {
//     return (
//       <Box
//         sx={{
//           display: 'flex',
//           flexDirection: 'column',
//           alignItems: 'center',
//           textAlign: 'center',
//           width: 120,
//         }}
//       >
//         {/* Avatar */}
//         <Avatar
//           src={thumbnail}
//           alt={title}
//           sx={{
//             width: 64,
//             height: 64,
//             mb: 1,
//             bgcolor: 'white',
//             cursor: 'pointer',
//           }}
//           onClick={handleClick}
//         />

//         {/* Title + Arrow in same row */}
//         <Tooltip title={title} arrow>
//           <Box
//             sx={{
//               display: 'flex',
//               alignItems: 'center',
//               justifyContent: 'center',
//               maxWidth: '100%',
//             }}
//           >
//             <Typography
//               variant="subtitle2"
//               fontWeight={400}
//               sx={{
//                 fontSize: '0.85rem',
//                 whiteSpace: 'nowrap',
//                 overflow: 'hidden',
//                 textOverflow: 'ellipsis',
//               }}
//             >
//               {title}
//             </Typography>
//             <IconButton
//               size="small"
//               onClick={e => {
//                 e.stopPropagation();
//                 toggleChildren();
//               }}
//               sx={{ml: 0.5, p: 0.25}}
//             >
//               {open ? (
//                 <ExpandLess fontSize="small" />
//               ) : (
//                 <ExpandMore fontSize="small" />
//               )}
//             </IconButton>
//           </Box>
//         </Tooltip>

//         {/* Subcategories list below */}
//         {open && (
//           <Box sx={{mt: 1, width: '100%'}}>
//             {isFetching ? (
//               <Typography variant="caption">Loading...</Typography>
//             ) : children.length > 0 ? (
//               <List dense sx={{width: '100%', bgcolor: '#f9f9f9'}}>
//                 {children.map(child => (
//                   <ListItemButton
//                     key={child.id}
//                     onClick={() =>
//                       router.push(`listing?collectionIds=${child.id}`)
//                     }
//                   >
//                     <ListItemText primary={child.name} />
//                   </ListItemButton>
//                 ))}
//               </List>
//             ) : (
//               <Typography variant="caption">No subcategories</Typography>
//             )}
//           </Box>
//         )}
//       </Box>
//     );
//   }

//   // IMAGE-based card styles
//   return (
//     <Box
//       sx={{
//         width: 200,
//         flex: '0 0 auto',
//         borderRadius: '16px',
//         overflow: 'hidden',
//         backgroundColor: '#fff',
//         cursor: 'pointer',
//         position: 'relative',
//         boxShadow: 1,
//         '&:hover': {
//           transform: 'scale(1.02)',
//         },
//       }}
//     >
//       {cardStyle === CardStyle.IMAGE_TITLE_SUBTITLE ? (
//         <Box sx={{height: 230, display: 'flex', flexDirection: 'column'}}>
//           <Box
//             sx={{
//               height: '40%',
//               backgroundColor: '#C8D3D5',
//               p: 1.5,
//               display: 'flex',
//               flexDirection: 'column',
//               justifyContent: 'center',
//             }}
//           >
//             <Typography variant="h6" fontWeight={700} sx={{color: '#0E1C1F'}}>
//               {title}
//             </Typography>
//             {subtitle && (
//               <Typography variant="caption" sx={{color: '#0E1C1F', mt: 0.5}}>
//                 {subtitle}
//               </Typography>
//             )}
//             <IconButton
//               size="small"
//               onClick={e => {
//                 e.stopPropagation();
//                 toggleChildren();
//               }}
//             >
//               {open ? <ExpandLess /> : <ExpandMore />}
//             </IconButton>
//           </Box>
//           <Box
//             sx={{
//               height: '100%',
//               backgroundImage: `url(${thumbnail})`,
//               backgroundSize: 'cover',
//               backgroundPosition: 'center',
//               flexGrow: 1,
//               position: 'relative',
//             }}
//             onClick={handleClick}
//           />
//         </Box>
//       ) : cardStyle === CardStyle.IMAGE_TITLE ? (
//         <Box
//           sx={{
//             height: 230,
//             backgroundImage: `url(${thumbnail})`,
//             backgroundSize: 'cover',
//             backgroundPosition: 'center',
//             position: 'relative',
//             display: 'flex',
//             alignItems: 'flex-start',
//             justifyContent: 'center',
//             p: 2,
//           }}
//         >
//           <Typography
//             variant="subtitle1"
//             fontWeight={600}
//             sx={{color: '#FFF', textShadow: '0 0 5px rgba(0,0,0,0.6)'}}
//           >
//             {title}
//           </Typography>
//           <IconButton
//             size="small"
//             onClick={e => {
//               e.stopPropagation();
//               toggleChildren();
//             }}
//             sx={{
//               position: 'absolute',
//               top: 8,
//               right: 8,
//               background: 'rgba(0,0,0,0.4)',
//               color: '#fff',
//             }}
//           >
//             {open ? <ExpandLess /> : <ExpandMore />}
//           </IconButton>
//         </Box>
//       ) : (
//         <Box
//           sx={{
//             height: 230,
//             backgroundImage: `url(${thumbnail})`,
//             backgroundSize: 'cover',
//             backgroundPosition: 'center',
//           }}
//         />
//       )}

//       {/* Child list below card */}
//       <Collapse in={open} timeout="auto" unmountOnExit>
//         <List dense sx={{background: '#f9f9f9'}}>
//           {isFetching ? (
//             <ListItemText primary="Loading..." />
//           ) : children.length > 0 ? (
//             children.map(child => (
//               <ListItemButton
//                 key={child.id}
//                 onClick={() => router.push(`listing?collectionIds=${child.id}`)}
//               >
//                 <ListItemText primary={child.name} />
//               </ListItemButton>
//             ))
//           ) : (
//             <ListItemText primary="No subcategories" />
//           )}
//         </List>
//       </Collapse>
//     </Box>
//   );
// };

// export default CollectionCard;
'use client';

import {useEffect, useState} from 'react';
import {
  Avatar,
  Box,
  Tooltip,
  Typography,
  IconButton,
  List,
  ListItemButton,
  ListItemText,
  Portal,
  CircularProgress,
} from '@mui/material';
import {ExpandMore, ExpandLess} from '@mui/icons-material';
import {CardStyle} from 'enums/pageSection.enum';
import {useRouter} from 'next/navigation';
import {SectionItem} from 'types/page-section';
import {useLazyGetCollectionsQuery} from 'redux/ecom/collectionApiSlice';

type Props = {
  item: SectionItem;
  cardStyle?: CardStyle;
};

const CollectionCard = ({item, cardStyle}: Props) => {
  const router = useRouter();
  const [open, setOpen] = useState(false);
  const [children, setChildren] = useState<any[]>([]);
  const [grandChildren, setGrandChildren] = useState<any[]>([]);
  const [, setSelectedParent] = useState<any>(null);
  const [selectedChild, setSelectedChild] = useState<any>(null);
  const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
  const [loadingChildrenIds, setLoadingChildrenIds] = useState<Set<string>>(
    new Set(),
  );
  const [childrenWithGrandchildren, setChildrenWithGrandchildren] = useState<
    Set<string>
  >(new Set());

  const [triggerGetCollections, {isFetching}] = useLazyGetCollectionsQuery();

  const handleClick = () => {
    router.push(`listing?collectionIds=${item.entityId}`);
  };

  // Fetch children categories of the root item
  const fetchChildren = async (parentId: string) => {
    try {
      const collections = await triggerGetCollections({
        where: {parentId},
      }).unwrap();
      setChildren(collections || []);

      // Determine which children have grandchildren
      const childrenWithGrand = new Set<string>();
      await Promise.all(
        (collections || []).map(async (child: any) => {
          const sub = await triggerGetCollections({
            where: {parentId: child.id},
          }).unwrap();
          if (sub && sub.length > 0) {
            childrenWithGrand.add(child.id);
          }
        }),
      );
      setChildrenWithGrandchildren(childrenWithGrand);
    } catch (err) {
      console.error('Failed to load children', err);
    }
  };

  // Toggle grandchildren list for a given child
  const toggleGrandChildren = async (child: any) => {
    if (selectedChild?.id === child.id) {
      // Close grandchildren if already selected
      setSelectedChild(null);
      setGrandChildren([]);
      return;
    }

    setSelectedChild(child);
    try {
      const collections = await triggerGetCollections({
        where: {parentId: child.id},
      }).unwrap();
      setGrandChildren(collections || []);
    } catch (err) {
      console.error('Failed to load grandchildren', err);
    }
  };

  const toggleMenu = (e?: React.MouseEvent<HTMLElement>) => {
    if (!open) {
      if (!item.entityId) return;
      fetchChildren(item.entityId);
      setSelectedParent(item);
      setGrandChildren([]);
      if (e) setAnchorEl(e.currentTarget);
    }
    setOpen(prev => !prev);
  };

  // Close dropdown on scroll
  useEffect(() => {
    const handleScroll = () => {
      setOpen(false);
    };

    if (open) {
      window.addEventListener('scroll', handleScroll, {passive: true});
    }

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [open]);

  // Close dropdown if anchorEl scrolls out of view
  useEffect(() => {
    const handleScroll = () => {
      if (!anchorEl) return;
      const rect = anchorEl.getBoundingClientRect();
      if (rect.bottom < 0 || rect.top > window.innerHeight) {
        setOpen(false);
      }
    };
    window.addEventListener('scroll', handleScroll, {passive: true});
    return () => window.removeEventListener('scroll', handleScroll);
  }, [anchorEl]);

  useEffect(() => {
    if (item.entityId) {
      fetchChildren(item.entityId);
    }
  }, [item.entityId]);

  const title = item.collection?.name || item.title || '';
  const subtitle = item.subtitle || '';
  const thumbnail = item.previewUrl || '';

  if (cardStyle === CardStyle.AVATAR_WITH_TITLE) {
    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          textAlign: 'center',
          width: 120,
          position: 'relative',
        }}
      >
        <Avatar
          src={thumbnail}
          alt={title}
          sx={{
            width: 64,
            height: 64,
            mb: 1,
            bgcolor: 'white',
            cursor: 'pointer',
          }}
          onClick={handleClick}
        />

        <Tooltip title={title} arrow>
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              maxWidth: '100%',
            }}
          >
            <Typography
              variant="subtitle2"
              fontWeight={400}
              sx={{
                fontSize: '0.85rem',
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
              }}
              onClick={handleClick}
            >
              {title}
            </Typography>

            {/* Show arrow button only if there are children */}
            {children.length > 0 && (
              <IconButton
                size="small"
                onClick={e => {
                  e.stopPropagation();
                  toggleMenu(e);
                }}
                sx={{ml: 0.5, p: 0.25}}
              >
                {open ? (
                  <ExpandLess fontSize="small" />
                ) : (
                  <ExpandMore fontSize="small" />
                )}
              </IconButton>
            )}
          </Box>
        </Tooltip>

        {open && anchorEl && (
          <Box
            sx={{
              position: 'fixed',
              top: anchorEl.getBoundingClientRect().bottom + window.scrollY,
              left: anchorEl.getBoundingClientRect().left + window.scrollX,
              bgcolor: '#fff',
              display: 'flex',
              boxShadow: 3,
              zIndex: 1300,
              transition: 'width 0.3s ease',
              width: selectedChild && grandChildren.length > 0 ? 600 : 300, // expand/shrink
              maxHeight: '80vh',
              overflow: 'hidden', // prevent scroll bleed between columns
            }}
          >
            {/* First Column - Children */}
            <Box
              sx={{
                flex: 1,
                borderRight:
                  selectedChild && grandChildren.length > 0
                    ? '1px solid #ddd'
                    : 'none',
                p: 1,
                overflowY: 'auto',
              }}
            >
              <List dense>
                {children.map(child => {
                  const hasGrandchildren = childrenWithGrandchildren.has(
                    child.id,
                  );
                  return (
                    <ListItemButton
                      key={child.id}
                      selected={selectedChild?.id === child.id}
                      onClick={() => {
                        if (!hasGrandchildren) {
                          router.push(`listing?collectionIds=${child.id}`);
                        }
                      }}
                    >
                      <ListItemText primary={child.name} />
                      {hasGrandchildren && (
                        <IconButton
                          size="small"
                          edge="end"
                          onClick={e => {
                            e.stopPropagation();
                            toggleGrandChildren(child);
                          }}
                        >
                          {selectedChild?.id === child.id &&
                          grandChildren.length > 0 ? (
                            <ExpandLess fontSize="small" />
                          ) : (
                            <ExpandMore fontSize="small" />
                          )}
                        </IconButton>
                      )}
                    </ListItemButton>
                  );
                })}
              </List>
            </Box>

            {/* Second Column - Grandchildren */}
            {selectedChild && grandChildren.length > 0 && (
              <Box
                sx={{
                  flex: 1,
                  p: 1,
                  overflowY: 'auto',
                  bgcolor: '#fafafa',
                }}
              >
                <List dense>
                  {grandChildren.map(grandChild => (
                    <ListItemButton
                      key={grandChild.id}
                      onClick={() => {
                        router.push(`listing?collectionIds=${grandChild.id}`);
                      }}
                    >
                      <ListItemText primary={grandChild.name} />
                    </ListItemButton>
                  ))}
                </List>
              </Box>
            )}
          </Box>
        )}
      </Box>
    );
  }

  // Keep image card styles unchanged, just replacing collapse with same two-column style
  return (
    <Box
      sx={{
        width: 200,
        flex: '0 0 auto',
        borderRadius: '16px',
        overflow: 'hidden',
        backgroundColor: '#fff',
        cursor: 'pointer',
        position: 'relative',
        boxShadow: 1,
        '&:hover': {
          transform: 'scale(1.02)',
        },
      }}
    >
      {cardStyle === CardStyle.IMAGE_TITLE_SUBTITLE ? (
        <Box sx={{height: 230, display: 'flex', flexDirection: 'column'}}>
          <Box
            sx={{
              height: '40%',
              backgroundColor: '#C8D3D5',
              p: 1.5,
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
            }}
          >
            <Typography variant="h6" fontWeight={700} sx={{color: '#0E1C1F'}}>
              {title}
            </Typography>
            {subtitle && (
              <Typography variant="caption" sx={{color: '#0E1C1F', mt: 0.5}}>
                {subtitle}
              </Typography>
            )}
            <IconButton
              size="small"
              onClick={e => {
                e.stopPropagation();
                toggleMenu();
              }}
            >
              {open ? <ExpandLess /> : <ExpandMore />}
            </IconButton>
          </Box>
          <Box
            sx={{
              height: '100%',
              backgroundImage: `url(${thumbnail})`,
              backgroundSize: 'cover',
              backgroundPosition: 'center',
              flexGrow: 1,
              position: 'relative',
            }}
            onClick={handleClick}
          />
        </Box>
      ) : cardStyle === CardStyle.IMAGE_TITLE ? (
        <Box
          sx={{
            height: 230,
            backgroundImage: `url(${thumbnail})`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            position: 'relative',
            display: 'flex',
            alignItems: 'flex-start',
            justifyContent: 'center',
            p: 2,
          }}
        >
          <Typography
            variant="subtitle1"
            fontWeight={600}
            sx={{color: '#FFF', textShadow: '0 0 5px rgba(0,0,0,0.6)'}}
          >
            {title}
          </Typography>
        </Box>
      ) : (
        <Box
          sx={{
            height: 230,
            backgroundImage: `url(${thumbnail})`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
          }}
        />
      )}

      {/* Two-column menu for image cards */}
      {open && (
        <Box
          sx={{
            position: 'absolute',
            top: '100%',
            left: 0,
            mt: 1,
            bgcolor: '#fff',
            display: 'flex',
            boxShadow: 3,
            zIndex: 10,
            minWidth: 400,
          }}
        >
          <List sx={{width: 200, borderRight: '1px solid #eee'}}>
            <ListItemButton selected>
              <ListItemText primary={title} />
            </ListItemButton>
          </List>
          <Box sx={{flex: 1, p: 1}}>
            {isFetching ? (
              <Typography variant="caption">Loading...</Typography>
            ) : children.length > 0 ? (
              <List dense>
                {children.map(child => (
                  <ListItemButton
                    key={child.id}
                    onClick={() =>
                      router.push(`listing?collectionIds=${child.id}`)
                    }
                  >
                    <ListItemText primary={child.name} />
                  </ListItemButton>
                ))}
              </List>
            ) : (
              <Typography variant="caption">No subcategories</Typography>
            )}
          </Box>
        </Box>
      )}
    </Box>
  );
};

export default CollectionCard;
