import {useEffect} from 'react';
import {<PERSON>, Button, <PERSON>ack, Typography} from '@mui/material';
import {ReviewStatus} from 'types/review';
import {User} from 'types/user-profile';
import {useRouter} from 'next/navigation';
import {useLazyGetSimilarProductsQuery} from 'redux/ecom/pageSectionApiSlice';
import ProductImages from './ProductImages';

export const SimilarProducts = ({
  title,
  isLoggedIn,
  user,
}: {
  title: string;
  isLoggedIn: boolean;
  user?: User;
}) => {
  const [fetchSimilarProducts, {data = [], isLoading}] =
    useLazyGetSimilarProductsQuery();

  useEffect(() => {
    fetchSimilarProducts({
      filter: {
        include: [
          {
            relation: 'featuredAsset',
            scope: {
              fields: {preview: true, id: true},
            },
          },
          {
            relation: 'product',
            required: true,
            scope: {
              fields: {description: true, id: true},
            },
          },
          {
            relation: 'productVariantPrice',
            scope: {
              fields: {
                price: true,
                mrp: true,
                currencyCode: true,
              },
            },
          },
          ...(isLoggedIn
            ? [
                {
                  relation: 'wishlist',
                  scope: {
                    where: {
                      deleted: false,
                      customerId: user?.profileId,
                    },
                    fields: {id: true},
                  },
                },
              ]
            : []),
          {
            relation: 'reviews',
            scope: {
              fields: {
                rating: true,
              },
              where: {
                status: ReviewStatus.APPROVED,
              },
            },
          },
        ],
        fields: {
          name: true,
          id: true,
          featuredAssetId: true,
          productId: true,
        },
        limit: 10,
      },
    });
  }, [fetchSimilarProducts]);

  const scrollGradientSx = {
    overflowX: 'auto',
    overflowY: 'hidden',
    pb: 2,
    '&::-webkit-scrollbar': {
      height: '7px',
    },
    '&::-webkit-scrollbar-track': {
      background: '#f0f0f0',
      borderTop: '1px solid #e0e0e0',
      borderBottom: '1px solid #e0e0e0',
    },
    '&::-webkit-scrollbar-thumb': {
      background: 'linear-gradient(to right, #5847F9, #00C9FF)',
      borderRadius: '10px',
      minWidth: '30px',
    },
  };

  const router = useRouter();

  return (
    <Stack flexDirection="column" gap={3} mt={3}>
      <Stack flexDirection="row" justifyContent="space-between">
        <Typography variant="h4" sx={{color: '#0A0A55'}}>
          {title}
        </Typography>
        <Button
          variant="outlined"
          sx={{
            color: '#707070',
            borderColor: '#707070',
            '&:hover': {
              borderColor: '#9E3393',
              color: '#00004F',
            },
          }}
          onClick={() => router.push('view-more?type=gift')}
        >
          View More
        </Button>
      </Stack>
      <Stack
        flexDirection="row"
        justifyContent="flex-start"
        gap={0}
        sx={{'& > *': {marginRight: '-50px'}, scrollGradientSx, mt: -4}}
      >
        {isLoading ? (
          <Typography>Loading...</Typography>
        ) : (
          data.map(variant => (
            <Box
              key={variant.id}
              sx={{minWidth: 240, maxWidth: 300, flex: '0 0 auto'}}
            >
              <ProductImages productVariant={variant} />
            </Box>
          ))
        )}
      </Stack>
    </Stack>
  );
};
