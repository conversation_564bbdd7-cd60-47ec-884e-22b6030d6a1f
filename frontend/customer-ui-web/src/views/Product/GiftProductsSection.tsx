import {useEffect} from 'react';
import {<PERSON>, <PERSON><PERSON>, <PERSON>ack, Typography} from '@mui/material';
import {useLazyGetProductVariantsQuery} from 'redux/ecom/ecomApiSlice';
import {ReviewStatus} from 'types/review';
import {User} from 'types/user-profile';
import {useRouter} from 'next/navigation';
import ViewedProductCard from './ViewedProductImageCard';

export const GiftProductsSection = ({
  title,
  isLoggedIn,
  user,
}: {
  title: string;
  isLoggedIn: boolean;
  user?: User;
}) => {
  const [fetchGiftVariants, {data = [], isLoading}] =
    useLazyGetProductVariantsQuery();

  useEffect(() => {
    fetchGiftVariants({
      include: [
        {
          relation: 'featuredAsset',
          scope: {
            fields: {preview: true, id: true},
          },
        },
        {
          relation: 'product',
          required: true,
          scope: {
            fields: {description: true, id: true},
            where: {isGiftWrapAvailable: true},
          },
        },
        {
          relation: 'productVariantPrice',
          scope: {
            fields: {
              price: true,
              mrp: true,
              currencyCode: true,
            },
          },
        },
        ...(isLoggedIn
          ? [
              {
                relation: 'wishlist',
                scope: {
                  where: {
                    deleted: false,
                    customerId: user?.profileId,
                  },
                  fields: {id: true},
                },
              },
            ]
          : []),
        {
          relation: 'reviews',
          scope: {
            fields: {
              rating: true,
            },
            where: {
              status: ReviewStatus.APPROVED,
            },
          },
        },
      ],
      fields: {
        name: true,
        id: true,
        featuredAssetId: true,
        productId: true,
      },
      limit: 10,
    });
  }, [fetchGiftVariants]);

  const router = useRouter();

  return (
    <Stack flexDirection="column" gap={3} mt={3}>
      <Stack flexDirection="row" justifyContent="space-between" mb={-5}>
        <Typography variant="h4" sx={{color: '#0A0A55'}}>
          {title}
        </Typography>
        <Button
          variant="outlined"
          sx={{
            color: '#707070',
            borderColor: '#707070',
            '&:hover': {
              borderColor: '#9E3393',
              color: '#00004F',
            },
          }}
          onClick={() => router.push('view-more?type=gift')}
        >
          View More
        </Button>
      </Stack>
      <Stack
        flexDirection="row"
        justifyContent="space-between"
        overflow="scroll"

        // sx={scrollGradientSx}
      >
        {isLoading ? (
          <Typography>Loading...</Typography>
        ) : (
          data.map(variant => (
            <Box
              key={variant.id}
              sx={{minWidth: 240, maxWidth: 300, flex: '0 0 auto'}}
            >
              <ViewedProductCard productVariant={variant} />
            </Box>
          ))
        )}
      </Stack>
    </Stack>
  );
};
