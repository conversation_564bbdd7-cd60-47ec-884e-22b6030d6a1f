'use client';

import React from 'react';
import {Box, Typography, Stack, Skeleton} from '@mui/material';
// import {ProductVariant} from 'types/product';

interface StoreNavTabsProps {
  storeData: {
    banner: string;
    logo?: string;
    dp?: string;
    storeName?: string;
    description?: string;
    legalName?: string;
    allowCategorisation?: boolean;
  };
  isLoading?: boolean;
  isError?: boolean;
  isNRLView?: boolean;
}

const StoreNavTabs: React.FC<StoreNavTabsProps> = ({
  storeData,
  isLoading,
  isError,
  isNRLView,
}) => {
  if (isLoading) {
    return (
      <Box>
        <Skeleton variant="rectangular" width="100%" height={360} />
        <Box
          sx={{
            p: 2,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}
        >
          <Stack direction="row" spacing={2} alignItems="center">
            <Skeleton variant="circular" width={50} height={50} />
            <Skeleton variant="text" width={150} height={30} />
          </Stack>
          <Stack
            direction="row"
            spacing={3}
            sx={{flexGrow: 1, justifyContent: 'center'}}
          >
            {[...Array(5)].map((_, i) => (
              <Skeleton key={i} variant="text" width={80} height={30} />
            ))}
          </Stack>
          <Skeleton variant="circular" width={50} height={50} />
        </Box>
      </Box>
    );
  }

  if (isError || !storeData) {
    return (
      <Box sx={{p: 3, textAlign: 'center', color: 'error.main'}}>
        <Typography variant="h6">
          Error loading store data. Please try again later.
        </Typography>
      </Box>
    );
  }

  const bannerImageUrl =
    storeData?.banner ||
    'https://getkuwa.com/cdn/shop/collections/Mama_Earth_fa698e64-340f-4feb-b363-29a311739041.png?v=1732790281';

  return (
    <Box sx={{mt: '-3%', mx: '-50px'}}>
      {isNRLView && (
        <Box
          sx={{
            background: '#A4A4A4',
            color: '#fff',
            textAlign: 'center',
            fontSize: '18px',
          }}
        >
          This is an exclusive store of {storeData.legalName}
        </Box>
      )}
      <Box
        sx={{
          position: 'relative',
          height: 360,
          backgroundImage: `url('${bannerImageUrl}')`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          display: 'flex',
          alignItems: 'center',
          px: 5,
          color: 'white',
          gap: 4,
        }}
      >
        <Box sx={{maxWidth: 600}}>
          <Typography variant="h2" sx={{fontWeight: 'bold', mb: 2}}>
            {storeData?.storeName || 'Store Name Not Available'}
          </Typography>
          <Typography variant="h5" sx={{fontWeight: 'bold', lineHeight: 1.5}}>
            {storeData?.description ||
              'Description not available. This brand finds and shares the energy of life from the pristine nature around the world.'}
          </Typography>
        </Box>
      </Box>
    </Box>
  );
};

export default StoreNavTabs;
