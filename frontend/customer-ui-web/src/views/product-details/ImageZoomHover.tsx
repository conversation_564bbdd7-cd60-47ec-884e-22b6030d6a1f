'use client';
import React, {useRef, useState, useEffect} from 'react';
import {Box} from '@mui/material';

interface ImageZoomProps {
  src: string;
  zoomScale?: number;
  disabled?: boolean;
  lensSize?: number;
}

export const ImageZoomHover = ({
  src,
  zoomScale = 2.5,
  disabled = false,
  lensSize = 150,
}: ImageZoomProps) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const imageRef = useRef<HTMLImageElement>(null);

  const [hovered, setHovered] = useState(false);
  const [hasMouseMoved, setHasMouseMoved] = useState(false);
  const [lensPosition, setLensPosition] = useState({x: 0, y: 0});
  const [zoomPosition, setZoomPosition] = useState({left: 0, top: 0});

  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageBounds, setImageBounds] = useState({
    left: 0,
    top: 0,
    width: 0,
    height: 0,
  });

  // Calculate image bounds when image loads or container resizes
  useEffect(() => {
    const updateImageBounds = () => {
      const container = containerRef.current;
      const image = imageRef.current;
      if (!container || !image || !imageLoaded) return;

      const containerRect = container.getBoundingClientRect();
      const imageAspectRatio = image.naturalWidth / image.naturalHeight;
      const containerAspectRatio = containerRect.width / containerRect.height;

      let imageWidth, imageHeight, imageLeft, imageTop;

      // Calculate actual rendered image dimensions (object-fit: contain)
      if (imageAspectRatio > containerAspectRatio) {
        // Image is wider - fit to container width
        imageWidth = containerRect.width;
        imageHeight = containerRect.width / imageAspectRatio;
        imageLeft = 0;
        imageTop = (containerRect.height - imageHeight) / 2;
      } else {
        // Image is taller - fit to container height
        imageHeight = containerRect.height;
        imageWidth = containerRect.height * imageAspectRatio;
        imageTop = 0;
        imageLeft = (containerRect.width - imageWidth) / 2;
      }

      setImageBounds({
        left: imageLeft,
        top: imageTop,
        width: imageWidth,
        height: imageHeight,
      });
    };

    updateImageBounds();
    window.addEventListener('resize', updateImageBounds);
    return () => window.removeEventListener('resize', updateImageBounds);
  }, [imageLoaded]);

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    const container = containerRef.current;
    if (!container || !imageLoaded) return;

    if (!hasMouseMoved) setHasMouseMoved(true);

    const containerRect = container.getBoundingClientRect();
    const lensHalf = lensSize / 2;

    // Get mouse position relative to container
    const mouseX = e.clientX - containerRect.left;
    const mouseY = e.clientY - containerRect.top;

    // Calculate lens position relative to the actual image bounds
    let lensX = mouseX - imageBounds.left;
    let lensY = mouseY - imageBounds.top;

    // Clamp lens position to stay within image boundaries
    lensX = Math.max(lensHalf, Math.min(imageBounds.width - lensHalf, lensX));
    lensY = Math.max(lensHalf, Math.min(imageBounds.height - lensHalf, lensY));

    // Store lens position relative to image
    setLensPosition({x: lensX, y: lensY});

    // Calculate zoom box position on screen
    const zoomWidth = lensSize * zoomScale;
    const zoomHeight = lensSize * zoomScale;

    let left = containerRect.right + 20;
    let top = containerRect.top;

    // Flip zoom box to left if overflowing
    if (left + zoomWidth > window.innerWidth) {
      left = containerRect.left - zoomWidth - 20;
    }

    if (left < 0) left = 0;
    if (top + zoomHeight > window.innerHeight) {
      top = window.innerHeight - zoomHeight - 20;
    }

    setZoomPosition({left, top});
  };

  if (disabled) {
    return (
      <Box
        ref={containerRef}
        sx={{
          width: '100%',
          // aspectRatio: '4 / 3',
          borderRadius: 4,
          overflow: 'hidden',
        }}
      >
        <img
          ref={imageRef}
          src={src}
          alt="Zoom"
          onLoad={() => setImageLoaded(true)}
          style={{
            width: '100%',
            height: '100%',
            objectFit: 'cover',
            borderRadius: 'inherit',
          }}
        />
      </Box>
    );
  }

  return (
    <>
      {/* 📷 Main Image */}
      <Box
        ref={containerRef}
        onMouseEnter={() => setHovered(true)}
        onMouseLeave={() => setHovered(false)}
        onMouseMove={handleMouseMove}
        sx={{
          width: '100%',
          aspectRatio: '4 / 3',
          position: 'relative',
          overflow: 'hidden',
          borderRadius: 4,
          cursor: 'zoom-in',
        }}
      >
        <img
          ref={imageRef}
          src={src}
          alt="Zoom"
          onLoad={() => setImageLoaded(true)}
          style={{
            width: '100%',
            height: '100%',
            objectFit: 'contain',
            display: 'block',
          }}
        />

        {/* 🔍 Lens Overlay */}
        {hovered && imageLoaded && (
          <Box
            sx={{
              position: 'absolute',
              top: `${imageBounds.top + lensPosition.y - lensSize / 2}px`,
              left: `${imageBounds.left + lensPosition.x - lensSize / 2}px`,
              width: `${lensSize}px`,
              height: `${lensSize}px`,
              backgroundColor: 'rgba(25, 118, 210, 0.2)',
              border: '2px solid #1976d2',
              backgroundImage: `
                linear-gradient(to right, rgba(25, 118, 210, 0.4) 1px, transparent 1px),
                linear-gradient(to bottom, rgba(25, 118, 210, 0.4) 1px, transparent 1px)
              `,
              backgroundSize: '10px 10px',
              pointerEvents: 'none',
              borderRadius: 1,
              zIndex: 2,
            }}
          />
        )}
      </Box>

      {/* 🔎 Zoomed Box */}
      {hovered && hasMouseMoved && imageLoaded && (
        <Box
          sx={{
            position: 'fixed',
            top: zoomPosition.top,
            left: zoomPosition.left,
            width: lensSize * zoomScale,
            height: lensSize * zoomScale,
            overflow: 'hidden',
            backgroundColor: '#fff',
            zIndex: 1300,
            border: '2px solid #1976d2',
            borderRadius: 1,
            boxShadow: '0 4px 20px rgba(0,0,0,0.15)',
          }}
        >
          <img
            src={src}
            alt="Zoomed"
            style={{
              position: 'absolute',
              // Calculate the zoom offset based on lens position within the image
              top: -(lensPosition.y - lensSize / 2) * zoomScale,
              left: -(lensPosition.x - lensSize / 2) * zoomScale,
              width: `${imageBounds.width * zoomScale}px`,
              height: `${imageBounds.height * zoomScale}px`,
              objectFit: 'contain',
              pointerEvents: 'none',
            }}
          />
        </Box>
      )}
    </>
  );
};
