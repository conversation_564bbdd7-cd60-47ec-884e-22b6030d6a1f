'use client';
import React, {useEffect, useState} from 'react';
import {
  Grid,
  <PERSON>pography,
  Button,
  Card,
  Box,
  Paper,
  InputBase,
  FormControl,
  Select,
  MenuItem,
  Pagination,
  SelectChangeEvent,
} from '@mui/material';

import {styled} from '@mui/system';
import {SearchNormal} from 'iconsax-react';
import {
  useGetWishlistsQuery,
  useLazyGetWishlistsCountQuery,
  useLazyGetWishlistsQuery,
  useRemoveItemFromWishlistMutation,
} from 'redux/ecom/wishlistApiSlice';
import {fieldsExcludeMetaFields} from 'types/api';
import {openSnackbar} from 'api/snackbar';
import {SnackbarProps} from 'types/snackbar';
import MainCard from 'components/MainCard';
import {Wishlist as WishlistType} from 'types/wishlist';
import WishlistItem from './WishlistItem';
import {IFilter} from 'types/filter';
import {useRouter} from 'next/navigation';

const StyledButton = styled(Button)({
  borderRadius: '20px',
  textTransform: 'none',
  padding: '8px 16px',
  maxHeight: '34px',
});

const Wishlist = () => {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState('');
  const [removeWishlistItem, {isLoading: isRemoving}] =
    useRemoveItemFromWishlistMutation();
  const [debouncedQuery, setDebouncedQuery] = useState('');
  const [wishlistData, setWishlistData] = useState<WishlistType[]>([]);
  const [selectedCollectionId, setSelectedCollectionId] = useState<
    string | null
  >(null);

  const [triggerWishlistSearch, {data, isLoading: isWishlistLoading}] =
    useLazyGetWishlistsQuery();
  const [
    triggerWishlistCount,
    {data: countData, isLoading: isCountLoading, isFetching: isCountFetching},
  ] = useLazyGetWishlistsCountQuery();
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
  });

  const {
    data: collectionData,
    isLoading: isCollectionsLoading,
    refetch,
  } = useGetWishlistsQuery({
    filter: {
      fields: {
        id: true,
      },
      include: [
        {
          relation: 'productVariant',
          scope: {
            include: [
              {
                relation: 'product',
                scope: {
                  include: [
                    {
                      relation: 'collection',
                      scope: {fields: {id: true, name: true}},
                    },
                  ],
                  fields: {id: true},
                },
              },
            ],
            fields: {id: true},
          },
        },
      ],
    },
  });

  useEffect(() => {
    refetch();
  }, [refetch]);

  const categories = React.useMemo(() => {
    const uniqueMap = new Map<string, string>();
    collectionData?.forEach(item => {
      const collection = item?.productVariant?.product?.collection;
      if (collection && !uniqueMap.has(collection.id)) {
        uniqueMap.set(collection.id, collection.name);
      }
    });
    return Array.from(uniqueMap.entries());
  }, [collectionData]);

  // Debounce logic
  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedQuery(searchQuery);
      setPagination(prev => ({...prev, page: 1}));
    }, 500);

    return () => clearTimeout(handler);
  }, [searchQuery]);

  useEffect(() => {
    const whereClause: any = {
      productVariant: {
        name: {ilike: `%${debouncedQuery}%`},
      },
    };

    if (selectedCollectionId) {
      whereClause.productVariant.product = {
        collectionId: selectedCollectionId,
      };
    }

    triggerWishlistCount({
      filter: {
        where: whereClause,
      },
    });
  }, [debouncedQuery, selectedCollectionId]);

  // API call when debounced query or collection changes
  useEffect(() => {
    const productVariantScope: IFilter = {
      where: {
        name: {ilike: `%${debouncedQuery}%`},
      },
      include: [
        {
          relation: 'product',
          required: true,
          scope: {
            fields: fieldsExcludeMetaFields,
            ...(selectedCollectionId && {
              where: {
                collectionId: selectedCollectionId,
              },
            }),
          },
        },
        {
          relation: 'productVariantPrice',
          scope: {fields: fieldsExcludeMetaFields},
        },
        {
          relation: 'featuredAsset',
          scope: {fields: fieldsExcludeMetaFields},
        },
      ],
      fields: fieldsExcludeMetaFields,
    };

    const filter = {
      include: [
        {
          relation: 'productVariant',
          required: true,
          scope: productVariantScope,
        },
      ],
      fields: fieldsExcludeMetaFields,
      limit: pagination.limit,
      skip: (pagination.page - 1) * pagination.limit,
    };

    triggerWishlistSearch({filter});
  }, [debouncedQuery, pagination.page, pagination.limit, selectedCollectionId]);

  useEffect(() => {
    if (data) setWishlistData(data);
    if (countData) setPagination(prev => ({...prev, total: countData.count}));
  }, [data, countData]);

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(event.target.value);
  };

  const handlePageChange = (
    event: React.ChangeEvent<unknown>,
    newPage: number,
  ) => {
    setPagination(prev => ({...prev, page: newPage}));
  };

  const handleRowsPerPageChange = (event: SelectChangeEvent<number>) => {
    setPagination(prev => ({
      ...prev,
      limit: Number(event.target.value),
      page: 1,
    }));
  };

  const handleCollectionChange = (collectionId: string | null) => {
    setSelectedCollectionId(collectionId);
    setPagination(prev => ({...prev, page: 1}));
  };

  const handleRemove = async (id: string) => {
    await removeWishlistItem(id).unwrap();
    openSnackbar({
      open: true,
      message: 'Removed from wishlist',
      variant: 'alert',
      alert: {color: 'success'},
    } as SnackbarProps);

    // Refetch with current filters
    const productVariantScope: IFilter = {
      where: {
        name: {ilike: `%${debouncedQuery}%`},
      },
      include: [
        {
          relation: 'product',
          required: true,
          scope: {
            fields: fieldsExcludeMetaFields,
            ...(selectedCollectionId && {
              where: {
                collectionId: selectedCollectionId,
              },
            }),
          },
        },
        {
          relation: 'productVariantPrice',
          scope: {fields: fieldsExcludeMetaFields},
        },
        {
          relation: 'featuredAsset',
          scope: {fields: fieldsExcludeMetaFields},
        },
      ],
      fields: fieldsExcludeMetaFields,
    };

    const filter = {
      include: [
        {
          relation: 'productVariant',
          scope: productVariantScope,
          required: true,
        },
      ],
      fields: fieldsExcludeMetaFields,
      limit: pagination.limit,
      skip: (pagination.page - 1) * pagination.limit,
    };

    triggerWishlistSearch({filter});

    const whereClause: any = {
      productVariant: {
        name: {ilike: `%${debouncedQuery}%`},
      },
    };

    if (selectedCollectionId) {
      whereClause.productVariant.product = {
        collectionId: selectedCollectionId,
      };
    }

    triggerWishlistCount({
      filter: {
        where: whereClause,
      },
    });
  };

  const noSearchResults =
    !isWishlistLoading &&
    wishlistData?.length === 0 &&
    (searchQuery !== '' || selectedCollectionId !== null);

  const isEntireWishlistEmpty =
    !isWishlistLoading &&
    !isCountLoading &&
    !isCountFetching &&
    searchQuery === '' &&
    selectedCollectionId === null &&
    pagination.total === 0;

  if (
    isWishlistLoading ||
    isCollectionsLoading ||
    isCountLoading ||
    isCountFetching
  ) {
    return <Typography>Loading wishlist...</Typography>;
  }
  if (isEntireWishlistEmpty) {
    return (
      <MainCard sx={{p: 5, borderRadius: 2, textAlign: 'center'}}>
        <Typography variant="h3" fontWeight="bold">
          No items in Wishlist.
        </Typography>
        <Typography variant="body1" mt={1}>
          Looks like your wishlist is empty. Start Browse to add items!
        </Typography>
        <Button
          variant="contained"
          sx={{mt: 3, borderRadius: '20px'}}
          onClick={() => router.push('/home')}
        >
          Back to Home
        </Button>
      </MainCard>
    );
  }

  return (
    <Card
      sx={{
        width: '100%',
        maxWidth: {xs: '100%', sm: '100%', md: '100%', lg: '100%'},
        p: {xs: 1, sm: 2},
        mb: 4,
      }}
    >
      <Paper
        elevation={0}
        sx={{p: {xs: 2, sm: 3}, bgcolor: 'white', borderRadius: 3}}
      >
        <Grid container spacing={2} alignItems="center" mb={3}>
          <Grid item xs={12} sm={3}>
            <Typography variant="subtitle1">Categories</Typography>
          </Grid>
          <Grid item xs={12} sm={9}>
            <Grid
              container
              spacing={1}
              sx={{
                flexWrap: 'nowrap',
                overflowX: 'auto',
                scrollbarWidth: 'none',
                msOverflowStyle: 'none',
                '&::-webkit-scrollbar': {
                  display: 'none',
                },
              }}
            >
              {[
                !isCollectionsLoading && ['all', 'All Products'],
                ...categories,
              ].map(([id, name]) => (
                <Grid item key={id} sx={{flex: '0 0 auto'}}>
                  <StyledButton
                    variant="outlined"
                    sx={{
                      minWidth: '6.5rem',
                      borderRadius: '1rem',
                      backgroundImage:
                        'linear-gradient(to right, #9A2D8E 50%, white 50%)',
                      backgroundSize: '200% 100%',
                      backgroundPosition:
                        selectedCollectionId === id ||
                        (id === 'all' && !selectedCollectionId)
                          ? 'left bottom'
                          : 'right bottom',
                      transition:
                        'background-position 0.4s ease-in-out, color 0.4s ease-in-out',
                      color:
                        selectedCollectionId === id ||
                        (id === 'all' && !selectedCollectionId)
                          ? 'white'
                          : 'black',
                      border: '0.5px solid black',
                      fontSize: {xs: '12px', sm: '14px'},
                      px: {xs: 1.5, sm: 2},
                      '&:hover': {
                        backgroundPosition: 'left bottom',
                        color: 'white',
                      },
                    }}
                    onClick={() =>
                      handleCollectionChange(id === 'all' ? null : id)
                    }
                  >
                    {name}
                  </StyledButton>
                </Grid>
              ))}
            </Grid>
          </Grid>
        </Grid>

        {/* Search Bar */}
        <Paper
          component="form"
          sx={{
            p: '5px 10px',
            display: 'flex',
            alignItems: 'center',
            borderRadius: '20px',
            border: '1px solid #ccc',
            backgroundColor: 'transparent',
            boxShadow: 'none',
            mb: 3,
          }}
        >
          <InputBase
            placeholder="Search Wishlist"
            inputProps={{'aria-label': 'search wishlist'}}
            sx={{flex: 1, fontSize: '14px'}}
            onChange={handleSearchChange}
            value={searchQuery}
          />
          <SearchNormal style={{padding: '4px'}} />
        </Paper>

        {/* Wishlist Items */}

        {noSearchResults ? (
          <Box sx={{textAlign: 'center', py: 5}}>
            <Typography variant="h4" color="textSecondary">
              No items found for your search.
            </Typography>
            <Typography variant="body1" color="textSecondary" mt={1}>
              Try adjusting your search terms or filters.
            </Typography>
          </Box>
        ) : (
          <Card
            sx={{
              backgroundColor: '#F8F8F8',
              borderRadius: 2,
              p: 2,
              mb: 2,
            }}
          >
            <Box display="flex" flexDirection="column" gap={2}>
              {wishlistData.map(item => (
                <WishlistItem
                  key={item.id}
                  item={item}
                  handleRemove={handleRemove}
                  isRemoving={isRemoving}
                />
              ))}
            </Box>
          </Card>
        )}
        {/* Pagination Controls */}
        {!noSearchResults && (
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              mt: 3,
            }}
          >
            <FormControl variant="standard" sx={{m: 1, minWidth: 120}}>
              <Select
                value={pagination.limit}
                onChange={handleRowsPerPageChange}
                label="Rows per page"
              >
                <MenuItem value={10}>10 per page</MenuItem>
                <MenuItem value={25}>25 per page</MenuItem>
                <MenuItem value={50}>50 per page</MenuItem>
              </Select>
            </FormControl>

            <Typography variant="body2" sx={{mx: 2}}>
              Showing {(pagination.page - 1) * pagination.limit + 1}-
              {Math.min(pagination.page * pagination.limit, pagination.total)}{' '}
              of {pagination.total}
            </Typography>

            <Pagination
              count={Math.ceil(pagination.total / pagination.limit)}
              page={pagination.page}
              onChange={handlePageChange}
              color="primary"
              shape="rounded"
            />
          </Box>
        )}
      </Paper>
    </Card>
  );
};

export default Wishlist;
