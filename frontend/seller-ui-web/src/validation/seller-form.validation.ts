import * as Yup from 'yup';

export const sellerFormValidation = Yup.object({
  fbId: Yup.string()
    .trim()
    .matches(/^(https?:\/\/)?(www\.)?facebook\.com\/[a-zA-Z0-9(\.\?)?]/, 'Please enter a valid Facebook link')
    .test('fb-or-insta', 'At least one social field is required: Facebook or Instagram', function (value) {
      const { instaId } = this.parent;
      return !!(value || instaId);
    }),

  instaId: Yup.string()
    .trim()
    .matches(/^(https?:\/\/)?(www\.)?facebook\.com\/[a-zA-Z0-9(\.\?)?]/, 'Please enter a valid Facebook link')

    .test('fb-or-insta', 'At least one social field is required: Facebook or Instagram', function (value) {
      const { fbId } = this.parent;
      return !!(value || fbId);
    }),

  website: Yup.string()
    .trim()
    .matches(/^(https?:\/\/)?(www\.)?([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}$/, 'Please enter a valid website URL')
    .nullable()
    .notRequired()
});

export const sampleProductValidationSchema = Yup.object({
  productName: Yup.string().required('Product Name is required'),
  images: Yup.array()
    .of(Yup.string().required('Image URL is required'))
    .min(1, 'At least one image is required')

    .required('Images field is required')
    .label('Sample Product Image')
});
