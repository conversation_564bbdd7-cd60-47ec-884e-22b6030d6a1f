'use client';

import { useState, ChangeEvent, Dispatch, SetStateAction, useEffect, useMemo } from 'react';
import { Box, Chip, Drawer, IconButton, InputAdornment, OutlinedInput, Stack, Typography, useTheme, useMediaQuery } from '@mui/material';

import { SearchNormal1 } from 'iconsax-react';

import MainCard from 'components/MainCard';
import SimpleBar from 'components/third-party/SimpleBar';

import { User } from 'types/user-profile';
import { ChatDto } from 'redux/app/types';
import { ThemeMode } from 'config';
import { useGetSellerChatsQuery } from 'redux/app/chat/chatApiSlice';
import ChatItem from './ChatItem';

export interface ExtendedUser extends User {
  presignedPhotoUrl?: string;
}

interface ChatDrawerProps {
  handleDrawerOpen: () => void;
  openChatDrawer: boolean;
  setUser: (u: ExtendedUser) => void;
  selectedUser: ExtendedUser | null;
  selectedChatId: string | null;
  setSelectedChat: Dispatch<SetStateAction<ChatDto | null>>;
  chat: ChatDto | null;
}

export default function ChatDrawer({ handleDrawerOpen, openChatDrawer, selectedChatId, setSelectedChat, chat }: ChatDrawerProps) {
  const theme = useTheme();
  const matchDownLG = useMediaQuery(theme.breakpoints.down('lg'));
  const drawerBG = theme.palette.mode === ThemeMode.DARK ? 'dark.main' : 'white';

  const [search, setSearch] = useState('');
  const [debouncedSearch, setDebouncedSearch] = useState('');

  const { data: sellerChats = [], refetch } = useGetSellerChatsQuery({ search: debouncedSearch });

  const handleSearch = (event: ChangeEvent<HTMLInputElement>) => {
    setSearch(event.target.value);
  };

  const filteredChats = useMemo(() => {
    return sellerChats.filter((chat) => {
      const customerUser = chat?.userData?.userTenant?.user;
      const firstName = customerUser?.firstName?.toLowerCase() || '';
      const lastName = customerUser?.lastName?.toLowerCase() || '';
      const keyword = search.toLowerCase();
      return firstName.includes(keyword) || lastName.includes(keyword);
    });
  }, [search, sellerChats]);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedSearch(search);
    }, 300);
    return () => clearTimeout(handler);
  }, [search]);

  return (
    <Drawer
      sx={{
        width: 320,
        flexShrink: 0,
        display: { xs: openChatDrawer ? 'block' : 'none', lg: 'block' },
        zIndex: { xs: openChatDrawer ? 1300 : -1, lg: 0 },
        '& .MuiDrawer-paper': {
          height: '100%',
          width: 320,
          boxSizing: 'border-box',
          position: matchDownLG ? 'fixed' : 'relative',
          border: 'none',
          ...(!matchDownLG && { borderRadius: '12px 0 0 12px' })
        }
      }}
      variant={matchDownLG ? 'temporary' : 'persistent'}
      anchor="left"
      open={openChatDrawer}
      ModalProps={{ keepMounted: true }}
      onClose={handleDrawerOpen}
    >
      <MainCard
        sx={{
          bgcolor: matchDownLG ? 'transparent' : drawerBG,
          borderRadius: '12px 0 0 12px',
          borderRight: 'none',
          height: '100%',
          '& div:nth-of-type(2)': {
            height: 'auto'
          }
        }}
        border={!matchDownLG}
        content={false}
      >
        <Box sx={{ p: 3, pb: 1 }}>
          <Stack spacing={2}>
            <Stack direction="row" spacing={0.5} alignItems="center">
              <Typography variant="h5" color="inherit">
                Messages
              </Typography>
              <Chip
                label={sellerChats.length}
                color={theme.palette.mode === ThemeMode.DARK ? 'default' : 'secondary'}
                sx={{ width: 20, height: 20, borderRadius: '50%' }}
              />
              <IconButton onClick={refetch} size="small" color="primary">
                🔄
              </IconButton>
            </Stack>

            <OutlinedInput
              fullWidth
              placeholder="Search by customer name"
              value={search}
              onChange={handleSearch}
              sx={{ '& .MuiOutlinedInput-input': { p: '10.5px 0px 12px' } }}
              startAdornment={
                <InputAdornment position="start">
                  <SearchNormal1 style={{ fontSize: 'small' }} />
                </InputAdornment>
              }
            />
          </Stack>
        </Box>

        <SimpleBar
          sx={{
            overflowX: 'hidden',
            height: matchDownLG ? 'calc(100vh - 300px)' : 'calc(100vh - 402px)',
            minHeight: matchDownLG ? 0 : 420
          }}
        >
          <Box sx={{ p: 3, pt: 0 }}>
            {filteredChats.length > 0 ? (
              filteredChats.map((chat) => (
                <ChatItem key={chat.id} chat={chat} isSelected={chat.id === selectedChatId} onClick={() => setSelectedChat(chat)} />
              ))
            ) : search.trim() === '' && chat ? (
              <ChatItem key={chat.id} chat={chat} isSelected={chat.id === selectedChatId} onClick={() => setSelectedChat(chat)} />
            ) : (
              <Typography>No messages found</Typography>
            )}
          </Box>
        </SimpleBar>
      </MainCard>
    </Drawer>
  );
}
