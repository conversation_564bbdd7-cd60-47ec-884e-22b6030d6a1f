'use client';

import React, { useEffect, useState } from 'react';
import { Box, Button, Grid, Table, TableBody, TableCell, TableContainer, TableRow, Typography } from '@mui/material';
import { Stack } from '@mui/system';
import MainCard from 'components/MainCard';
import SmallCardContent from 'components/forms/SmallCardContent';
import { DatePicker, LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { useGetproductsQuery } from 'redux/app/products/productApiSlice';
import { useGetOrderQuery } from 'redux/app/order/orderApiSlice';
import { IFilter } from 'redux/app/types/filter';
import { getPaymentLabel } from 'enums/payment.enum';
import { useFeatureAccess } from 'hooks/useFeatureAccess';
import { useGetUserQuery } from 'redux/auth/authApiSlice';

const getStatusColor = (status: string) => {
  switch (status.toLowerCase()) {
    case 'new':
      return '#1976d2';
    case 'accepted':
      return '#388e3c';
    case 'processing':
      return '#fbc02d';
    case 'dispatched':
      return '#0288d1';
    case 'delivered':
      return '#2e7d32';
    case 'rejected':
      return '#d32f2f';
    case 'return_refund':
      return '#ef6c00';
    case 'refund_completed':
      return '#7cb342';
    case 'pending':
      return '#ffa000';
    default:
      return '#9e9e9e';
  }
};

const AnalyticsPage = () => {
  const [startDate, setStartDate] = useState<Date | null>(null);
  const [endDate, setEndDate] = useState<Date | null>(null);
  const dateWhereFilter =
    startDate && endDate
      ? {
          createdOn: {
            gte: startDate.toISOString(),
            lte: endDate.toISOString()
          }
        }
      : undefined;

  const filter: IFilter | undefined = dateWhereFilter ? { where: dateWhereFilter } : undefined;
  const { data: user } = useGetUserQuery();
  const sellerId = user?.profileId ?? '';

  const { data: allOrders = [] } = useGetOrderQuery({ filter });
  const { data: allProducts = [], isLoading: isProductListLoading } = useGetproductsQuery(filter);
  const hasFeature = useFeatureAccess();

  const canAllowNrl = hasFeature('no-redirection-link');

  const orderLineItemFilter =
    startDate && endDate
      ? {
          where: {
            and: [{ createdOn: { gte: startDate.toISOString() } }, { createdOn: { lte: endDate.toISOString() } }]
          },
          include: [
            {
              relation: 'order',
              required: true
            },
            {
              relation: 'productVariant',
              required: true,
              scope: {
                include: [{ relation: 'product' }]
              }
            }
          ]
        }
      : {
          include: [
            {
              relation: 'order',
              required: true
            },
            {
              relation: 'productVariant',
              required: true,
              scope: {
                include: [{ relation: 'product' }]
              }
            }
          ]
        };

  const { data: orderLineItems = [], isLoading: isOrderPriceLoading } = useGetOrderQuery({
    filter: orderLineItemFilter
  });

  const totalSalesAmount = orderLineItems.reduce((sum, item) => sum + Number(item.totalPrice ?? 0), 0);
  useEffect(() => {
    if (allProducts.length > 0 && !startDate) {
      const productDates = allProducts.map((product) => new Date(product.createdOn)).filter((d) => !isNaN(d.getTime()));

      const earliestDate = new Date(Math.min(...productDates.map((d) => d.getTime())));
      setStartDate(earliestDate);

      if (!endDate) {
        setEndDate(new Date());
      }
    }
  }, [allProducts, startDate, endDate]);

  return (
    <Box sx={{ mt: 4, mb: 4, px: { xs: 2, md: 8 } }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">Dashboard</Typography>
        {canAllowNrl && (
          <Button
            href={`${process.env.NEXT_PUBLIC_SELLER_STORE_URL}/seller-stores/${sellerId}?nrl=true`}
            variant="outlined"
            sx={{
              textTransform: 'none',
              px: 3,
              color: '#00004F',
              borderColor: '#707070',
              '&:hover': {
                borderColor: '#9E3393',
                color: '#00004F'
              }
            }}
          >
            View My Store
          </Button>
        )}
      </Box>

      <Grid container spacing={3} justifyContent="center">
        <Grid container item xs={12} spacing={3} justifyContent="center">
          <Grid item xs={12}>
            <MainCard>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <Stack direction={{ xs: 'column', sm: 'row' }} alignItems="center" justifyContent="space-between" gap={2}>
                    <LocalizationProvider dateAdapter={AdapterDateFns}>
                      <DatePicker label="Start Date" value={startDate} onChange={(newValue) => setStartDate(newValue)} />
                    </LocalizationProvider>

                    <LocalizationProvider dateAdapter={AdapterDateFns}>
                      <DatePicker label="End Date" value={endDate} onChange={(newValue) => setEndDate(newValue)} />
                    </LocalizationProvider>
                  </Stack>
                </Grid>
                <Grid item xs={12}>
                  <Grid container direction="row" spacing={3} justifyContent="space-between">
                    <Grid item xs={12} sm={4}>
                      <SmallCardContent
                        title="New Orders 2"
                        image="/assets/images/icons/total-sales.svg"
                        subtitle="Total Sales"
                        amount={
                          isOrderPriceLoading
                            ? 'Loading...'
                            : `₹ ${totalSalesAmount.toLocaleString(undefined, { maximumFractionDigits: 2 })}`
                        }
                        color={'#9E3393'}
                      />
                    </Grid>
                    <Grid item xs={12} sm={6} md={4}>
                      <SmallCardContent
                        title="Total Order"
                        image="/assets/images/icons/total-order.svg"
                        subtitle="Total Order"
                        amount={isOrderPriceLoading ? 'Loading...' : `${allOrders.length}`}
                        color={'#A4A4A4'}
                      />
                    </Grid>
                    <Grid item xs={12} sm={4}>
                      <SmallCardContent
                        title="Total Products"
                        image="/assets/images/icons/total-product.svg"
                        subtitle="Total Products"
                        amount={isProductListLoading ? 'Loading...' : `${allProducts.length}`}
                        color={'#00004F'}
                      />
                    </Grid>
                  </Grid>
                </Grid>
              </Grid>
            </MainCard>
          </Grid>
        </Grid>
        <Grid container item xs={12} spacing={3} justifyContent="center">
          <Grid item xs={12} sm={6} md={4}>
            <SmallCardContent
              title="Total Payment Due :"
              image="/assets/images/icons/total-sales.svg"
              subtitle="Total Payment Due :"
              amount="₹ 1,975,512"
              color={'#9E3393'}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={4}>
            <SmallCardContent
              title="Total Payment Received :"
              image="/assets/images/icons/total-order.svg"
              subtitle="Total Payment Received :"
              amount="700"
              color={'#A4A4A4'}
            />
          </Grid>
        </Grid>
        <Grid item xs={12} sx={{ display: 'flex', justifyContent: 'center' }}>
          <MainCard
            sx={{
              width: { xs: '100%' },
              minHeight: '100%',
              overflow: 'hidden',
              boxShadow: 'none'
            }}
          >
            <Typography variant="h5"> Latest Orders</Typography>
            <TableContainer
              component={Box}
              sx={{
                maxHeight: '400px',
                overflowY: 'auto'
              }}
            >
              {isOrderPriceLoading ? (
                <Typography variant="body1" sx={{ mt: 2 }}>
                  Loading latest orders...
                </Typography>
              ) : orderLineItems.length === 0 ? (
                <Typography variant="h6" sx={{ mt: 2, textAlign: 'center' }}>
                  No orders found
                </Typography>
              ) : (
                <Table>
                  <TableBody>
                    {orderLineItems.map((item, index) => (
                      <TableRow key={index}>
                        <TableCell align="center">Order ID :{item.order.orderId ?? '---'}</TableCell>
                        <TableCell align="center"> Product ID :{item.productVariant?.product?.productId ?? '---'}</TableCell>
                        <TableCell align="left"> Product Price : {item.totalPrice}</TableCell>
                        <TableCell align="left">Payment: {getPaymentLabel(item.order.paymentMethod)}</TableCell>
                        <TableCell align="center">
                          <span
                            style={{
                              color: 'white',
                              backgroundColor: getStatusColor(item.status),
                              padding: '5px 10px',
                              borderRadius: '22px',
                              display: 'inline-block',
                              minWidth: '120px'
                            }}
                          >
                            {item.status}
                          </span>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </TableContainer>
          </MainCard>
        </Grid>
      </Grid>
    </Box>
  );
};

export default AnalyticsPage;
