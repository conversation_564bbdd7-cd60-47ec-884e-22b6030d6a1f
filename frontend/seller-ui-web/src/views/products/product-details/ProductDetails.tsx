import { Card, CardContent, Typography, Divider, Box } from '@mui/material';
import { InfoGroup } from './InfoGroup';
import { Product } from 'types/product';
import { renderRichText } from 'utils/richTextUtils';

export default function ProductDetails({ product }: { product: Product }) {
  const hasData =
    product?.collection?.name ||
    (product.productFacetValues && product.productFacetValues.length > 0) ||
    product.productDetail?.details ||
    product.productMoreInfo?.info ||
    product.productSuitability?.suitableFor ||
    (product.productBoxContents && product.productBoxContents.length > 0) ||
    product.productUniqueness?.uniqueness ||
    product.productPersonalWork?.workLevel;

  return (
    <Card variant="outlined" sx={{ borderRadius: 2 }}>
      <CardContent>
        <Typography variant="h5" gutterBottom>
          Product Details
        </Typography>
        <Divider sx={{ mb: 1 }} />

        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            gap: 2,
            width: '100%',
            maxHeight: '360px',
            overflow: 'auto',
            pr: 1,
            '&::-webkit-scrollbar': { height: '6px', width: '6px' },
            '&::-webkit-scrollbar-thumb': {
              backgroundColor: 'divider',
              borderRadius: '3px'
            },
            '&::-webkit-scrollbar-track': { backgroundColor: 'transparent' }
          }}
        >
          {!hasData ? (
            <Typography variant="body2" color="text.secondary">
              <strong>No data found</strong>
            </Typography>
          ) : (
            <>
              <InfoGroup
                title="Category"
                items={[
                  {
                    label: 'Collection',
                    value: product?.collection?.name
                  }
                ]}
              />

              <InfoGroup
                title="Tag"
                items={
                  product.productFacetValues?.map((d) => ({
                    label: d.facetValue.name,
                    value: d.facetValue.facet.name
                  })) || []
                }
              />

              <InfoGroup
                title="Details"
                items={
                  product.productDetail?.details
                    ? [
                        {
                          label: 'Detail',
                          value: (
                            <div
                              dangerouslySetInnerHTML={{
                                __html: renderRichText(product.productDetail.details)
                              }}
                            />
                          )
                        }
                      ]
                    : []
                }
              />

              <InfoGroup
                title="More Info"
                items={
                  product.productMoreInfo
                    ? [
                        {
                          label: 'Info',

                          // value: product.productMoreInfo.info
                          value: (
                            <div
                              dangerouslySetInnerHTML={{
                                __html: renderRichText(product.productMoreInfo.info)
                              }}
                            />
                          )
                        }
                      ]
                    : []
                }
              />

              <InfoGroup
                title="Suitability"
                items={
                  product.productSuitability?.suitableFor
                    ? [
                        {
                          label: 'Suitable For',
                          // value: product.productSuitability.suitableFor
                          value: (
                            <div
                              dangerouslySetInnerHTML={{
                                __html: renderRichText(product.productSuitability.suitableFor)
                              }}
                            />
                          )
                        }
                      ]
                    : []
                }
              />

              <InfoGroup
                title="Box Contents"
                items={
                  product.productBoxContents?.map((d) => ({
                    label: d.itemName,
                    value: `Qty: ${d.quantity}`
                  })) || []
                }
              />

              <InfoGroup
                title="Uniqueness"
                items={
                  product.productUniqueness?.uniqueness
                    ? [
                        {
                          label: 'Unique Feature',
                          // value: product.productUniqueness.uniqueness
                          value: (
                            <div
                              dangerouslySetInnerHTML={{
                                __html: renderRichText(product.productUniqueness.uniqueness)
                              }}
                            />
                          )
                        }
                      ]
                    : []
                }
              />

              <InfoGroup
                title="Personal Work"
                items={
                  product.productPersonalWork?.workLevel
                    ? [
                        {
                          label: 'Work Level',
                          // value: product.productPersonalWork.workLevel
                          value: (
                            <div
                              dangerouslySetInnerHTML={{
                                __html: renderRichText(product.productUniqueness.uniqueness)
                              }}
                            />
                          )
                        }
                      ]
                    : []
                }
              />
            </>
          )}
        </Box>
      </CardContent>
    </Card>
  );
}
