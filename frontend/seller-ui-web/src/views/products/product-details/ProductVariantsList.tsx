'use client';

import React, { useState } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  Paper,
  IconButton,
  Tooltip,
  Stack,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Box
} from '@mui/material';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { Edit, Trash } from 'iconsax-react';

import {
  useCreatePinnedProductMutation,
  useDeletePinnedProductMutation,
  useDeleteProductVariantByIdMutation,
  useGetPinnedProductsQuery
} from 'redux/app/products/productApiSlice';

import { ProductVariant } from 'types/product-variant';
import { useFeatureAccess } from 'hooks/useFeatureAccess';
import { openSnackbar } from 'api/snackbar';
import { SnackbarProps } from 'types/snackbar';
import { Button } from '@mui/material';

type Props = {
  productVariants: ProductVariant[];
  hideTitle?: boolean;
  callback?: string;
  refetch: () => void;
};

export default function ProductVariantsTable({ productVariants, refetch, hideTitle, callback }: Props) {
  const router = useRouter();
  const hasFeature = useFeatureAccess();

  const canPinnedProducts = hasFeature('pin-products-to-top');

  const { data: pinnedProducts = [], refetch: refetchPinned } = useGetPinnedProductsQuery({});
  const [createPinnedProduct] = useCreatePinnedProductMutation();
  const [deletePinnedProduct] = useDeletePinnedProductMutation();
  const [loading, setLoading] = useState<string | null>(null);
  const [deleteProductVariantById] = useDeleteProductVariantByIdMutation();
  const [openDeleteModal, setOpenDeleteModal] = useState(false);
  const [selectedVariantId, setSelectedVariantId] = useState<string | null>(null);
  const variantIdToProductIdMap = new Map<string, string>(productVariants.map((v) => [v.id, v.productId]));

  const getPinnedVariantForProduct = (productId: string): string | undefined => {
    for (const pinned of pinnedProducts) {
      const variantProductId = variantIdToProductIdMap.get(pinned.productVariantId);
      if (variantProductId === productId) {
        return pinned.productVariantId;
      }
    }
    return undefined;
  };

  const handlePin = async (variantToPin: ProductVariant) => {
    setLoading(variantToPin.id);
    const productId = variantToPin.productId;
    if (!productId) return;

    const currentPinned = pinnedProducts.find((pinned) => {
      const match = productVariants.find((v) => v.id === pinned.productVariantId);
      return match?.productId === productId;
    });

    const isSame = currentPinned?.productVariantId === variantToPin.id;

    if (isSame && currentPinned?.id) {
      await deletePinnedProduct(currentPinned.id).unwrap();
    } else {
      if (currentPinned?.id) {
        await deletePinnedProduct(currentPinned.id).unwrap();
        await refetchPinned();
      }
      await createPinnedProduct({ productVariantId: variantToPin.id }).unwrap();
    }

    await refetchPinned();
    setLoading(null);
  };

  const handleDelete = async () => {
    if (selectedVariantId) {
      await deleteProductVariantById(selectedVariantId).unwrap();
      openSnackbar({
        open: true,
        message: 'Product Variant deleted successfully.',
        variant: 'alert',
        alert: { color: 'success' }
      } as SnackbarProps);
      await refetch();

      setOpenDeleteModal(false);
    }
  };

  if (!productVariants?.length) {
    return (
      <Typography variant="body2" color="text.secondary">
        <strong>No data found</strong>
      </Typography>
    );
  }

  return (
    <>
      {!hideTitle && (
        <Typography variant="h5" gutterBottom>
          Product Variants
        </Typography>
      )}

      <TableContainer component={Paper} elevation={1}>
        <Table size="small">
          <TableHead>
            <TableRow>
              <TableCell>
                <strong>Image</strong>
              </TableCell>
              <TableCell>
                <strong>Name</strong>
              </TableCell>
              <TableCell>
                <strong>SKU</strong>
              </TableCell>
              <TableCell>
                <strong>Actions</strong>
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {productVariants.map((variant) => {
              const currentlyPinnedVariantId = getPinnedVariantForProduct(variant.productId);
              const pinned = currentlyPinnedVariantId === variant.id;

              return (
                <TableRow key={variant.id}>
                  <TableCell>
                    {variant.featuredAsset?.previewUrl ? (
                      <Image
                        src={variant.featuredAsset.previewUrl}
                        alt={variant.name}
                        width={40}
                        height={40}
                        style={{ objectFit: 'cover', borderRadius: 4 }}
                      />
                    ) : (
                      <span>—</span>
                    )}
                  </TableCell>
                  <TableCell>{variant.name}</TableCell>
                  <TableCell>{variant.sku}</TableCell>
                  <TableCell>
                    <Stack direction="row" spacing={1}>
                      <Tooltip title="Edit Product Variant">
                        <IconButton
                          color="primary"
                          onClick={() => router.push(`/products/variants/${variant.id}?callback=${callback ?? '/products'}`)}
                        >
                          <Edit />
                        </IconButton>
                      </Tooltip>
                      {canPinnedProducts && (
                        <Tooltip title={pinned ? 'Unpin Variant' : 'Pin this Variant'}>
                          <IconButton
                            onClick={() => handlePin(variant)}
                            color={pinned ? 'success' : 'default'}
                            disabled={loading === variant.id}
                          >
                            <Image
                              src={pinned ? '/assets/images/icons8-pin-25.png' : '/assets/images/icons8-pin-48.png'}
                              alt={pinned ? 'Pinned' : 'Unpinned'}
                              width={24}
                              height={24}
                            />
                          </IconButton>
                        </Tooltip>
                      )}
                      <Tooltip title="Delete Product Variant">
                        <IconButton
                          color="error"
                          onClick={() => {
                            setSelectedVariantId(variant.id);
                            setOpenDeleteModal(true);
                          }}
                        >
                          <Trash />
                        </IconButton>
                      </Tooltip>
                    </Stack>
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </TableContainer>
      <Dialog open={openDeleteModal} onClose={() => setOpenDeleteModal(false)} aria-labelledby="delete-confirmation-dialog">
        <DialogTitle id="delete-confirmation-dialog">
          <Typography variant="h6" sx={{ fontWeight: 600, color: 'text.primary' }}>
            Delete Product Variant
          </Typography>
        </DialogTitle>
        <DialogContent>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Typography variant="h6">
              Are you sure you want to permanently delete this product variant? Deleting this variant will remove it from your store, and
              customers will no longer be able to purchase it.
            </Typography>
          </Box>
        </DialogContent>
        <DialogActions sx={{ justifyContent: 'flex-end' }}>
          <Button onClick={() => setOpenDeleteModal(false)} color="primary" variant="outlined" sx={{ width: 120 }}>
            Cancel
          </Button>
          <Button onClick={handleDelete} color="error" variant="contained" sx={{ width: 120 }}>
            Yes, Delete
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
}
