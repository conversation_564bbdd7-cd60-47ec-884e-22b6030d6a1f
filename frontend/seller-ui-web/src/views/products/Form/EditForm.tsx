'use client';

import { useState, useEffect, useMemo, ReactNode } from 'react';

// next
import { useRouter } from 'next/navigation';

// material-ui
import Grid from '@mui/material/Grid';
import TextField from '@mui/material/TextField';
import InputLabel from '@mui/material/InputLabel';

// project-imports
import MainCard from 'components/MainCard';

// assets
import {
  Autocomplete,
  Box,
  CardActions,
  CircularProgress,
  FormControlLabel,
  FormHelperText,
  Radio,
  RadioGroup,
  Button,
  Switch,
  Tooltip
} from '@mui/material';
import { useFormik } from 'formik';
import { ProductDto } from 'types/product-dto';
import { useLazyGetCollectionsQuery } from 'redux/app/collections/collectionApiSlice';
import { AutoCompleteOption } from 'types/common';
import { ProductAsset } from './ProductAsset';
import {
  useCreateAssetMutation,
  useGetAssetsCountQuery,
  useGetAssetsQuery,
  useUpdateProductByIdMutation
} from 'redux/app/products/productApiSlice';
import { ProductCustomizationForm } from './Customization';
import { ProductMetaForm } from './ProductMetaForm';
import { useLazyGetFacetsQuery } from 'redux/app/facet/facetApiSlice';
import { LoadingButton } from '@mui/lab';
import { openSnackbar } from 'api/snackbar';
import { SnackbarProps } from 'types/snackbar';
import { Collection } from 'types/collection';
import { Facet } from 'types/facet';
import { Asset, ProductFacetValue } from 'types/product';
import { useGetTaxCategoriesQuery } from 'redux/app/tax/taxCategoryApiSlice';
import { TaxCategory } from 'types/product';
import { ProductHighlights } from './ProductHighlights';
import { ProductLegalForm } from './ProductLegalForm';
import { productSchema } from 'validation/product';
import ProductVariantsTable from '../product-details/ProductVariantsList';
import { ProductVariant } from 'types/product-variant';
import { useFeatureAccess } from 'hooks/useFeatureAccess';
import { Typography } from '@mui/material';

interface AssetDto {
  id: string;
  preview: string;
  mimeType: string;
}

type Props = {
  initialValue: ProductDto;
  initialCollection: Collection;
  initialFacets: ProductFacetValue[];
  initialTaxCategory: TaxCategory;
  initialAssets: AssetDto[];
  productId: string;
  productVariants: ProductVariant[];
  refetch: () => void;
};

function EditProduct({
  initialValue,
  initialCollection,
  initialAssets,
  productId,
  initialTaxCategory,
  initialFacets,
  productVariants,
  refetch
}: Props) {
  const router = useRouter();
  const [collectionInput, setCollectionInput] = useState<string>('');
  const [facetInput, setFacetInput] = useState<string>('');
  const [openGallery, setOpenGallery] = useState<boolean>(false);
  const [selectedAssets, setSelectedAssets] = useState<AssetDto[]>(initialAssets);
  const [, setTaxCategoryInput] = useState('');
  const hasFeature = useFeatureAccess();

  const [getCollection, { isLoading: collectionLoading, data: collections }] = useLazyGetCollectionsQuery({});
  const [getFacets, { isLoading: facetLoading, data: facets }] = useLazyGetFacetsQuery({});
  const { data: taxCategories, isLoading: taxCategoryLoading } = useGetTaxCategoriesQuery({});

  const [addAsset, { isLoading: isAssetUploading }] = useCreateAssetMutation();
  const [updateProduct, { isLoading: productCreateLoading }] = useUpdateProductByIdMutation();

  const [page, setPage] = useState(0);
  const [assets, setAssets] = useState<Asset[]>([]);
  const limit = 10;
  const { data: totalAssets } = useGetAssetsCountQuery();
  const { data: paginatedData, refetch: refetchAsset } = useGetAssetsQuery({ order: ['createdOn DESC'], limit, skip: page * limit });

  const fetchCollections = async () => {
    await getCollection({
      where: { name: { ilike: `%${collectionInput}%` } },
      include: [
        {
          relation: 'childrens'
        }
      ]
    }).unwrap();
  };

  const fetchFacets = async () => {
    await getFacets({
      where: { name: { ilike: `%${facetInput}%` } },
      include: [
        {
          relation: 'facetValues'
        }
      ]
    }).unwrap();
  };

  const collectionOptions: AutoCompleteOption[] = useMemo(() => {
    const values = collections?.length ? collections : [initialCollection];
    return values.map((item) => ({ label: item?.name, value: item.id })) ?? [];
  }, [initialCollection, collections]);

  const taxCategoryOptions: AutoCompleteOption[] = useMemo(() => {
    const values = taxCategories?.length ? taxCategories : [initialTaxCategory];
    return (
      values?.map((item) => ({
        label: `${item?.name} `,
        value: item?.id ?? ''
      })) ?? []
    );
  }, [initialTaxCategory, taxCategories]);

  const facetOptions: AutoCompleteOption[] = useMemo(() => {
    const formatFacet = (): Facet[] => {
      return initialFacets.map((item) => ({
        ...item.facetValue?.facet,
        id: item.facetValue?.facet?.id ?? '',
        facetValues: [
          {
            id: item.facetValueId,
            createdOn: '',
            name: item.facetValue?.name,
            code: item.facetValue.code,
            facetId: item.facetValue?.facet?.id ?? ''
          }
        ]
      }));
    };

    const values = facets?.length ? facets : formatFacet();

    return (
      values?.flatMap((facet) =>
        facet?.facetValues?.map((fv) => ({
          label: `${facet.name} - ${fv.name}`,
          value: fv.id
        }))
      ) ?? []
    );
  }, [facets, initialFacets]);

  const handleSubmit = async (values: ProductDto) => {
    const payload = {
      ...values,
      averageWeight: values.averageWeight !== null && values.averageWeight !== undefined ? Number(values.averageWeight) : undefined,
      isGiftWrapCharge: Number(values.isGiftWrapCharge)
    };
    await updateProduct({ id: productId, body: payload }).unwrap();
    openSnackbar({
      open: true,
      message: 'Product updated successfully',
      variant: 'alert',
      alert: { color: 'success' }
    } as SnackbarProps);
    router.push('/products');
  };

  const formik = useFormik<ProductDto>({
    initialValues: initialValue,
    validationSchema: productSchema,
    onSubmit: (values) => {
      handleSubmit(values);
    }
  });

  const handleFileChange = async (file: File) => {
    if (!file) return;
    const formData = new FormData();
    formData.append('file', file);
    await addAsset(formData).unwrap();
    refetchAsset();
  };

  useEffect(() => {
    if (selectedAssets?.length) {
      const hasFeaturedAsset = selectedAssets.some((asset) => asset.id === formik.values.featuredAssetId);

      if (!hasFeaturedAsset) {
        formik.setFieldValue('featuredAssetId', selectedAssets[0].id);
      }
    } else {
      formik.setFieldValue('featuredAssetId', '');
    }
    formik.setFieldValue(
      'assets',
      selectedAssets.map((asset) => asset.id)
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedAssets]);

  useEffect(() => {
    if (!collectionInput) return;
    const debounceTimeout = setTimeout(fetchCollections, 300);
    return () => clearTimeout(debounceTimeout);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [collectionInput]);

  useEffect(() => {
    if (!facetInput) return;
    const debounceTimeout = setTimeout(fetchFacets, 300);
    return () => clearTimeout(debounceTimeout);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [facetInput]);

  useEffect(() => {
    if (paginatedData) {
      setAssets((prev) => {
        const assetMap = new Map();
        prev.forEach((asset) => assetMap.set(asset.id, asset));
        paginatedData.forEach((asset) => assetMap.set(asset.id, asset));
        return Array.from(assetMap.values());
      });
    }
  }, [paginatedData]);

  const hasMore = useMemo(() => {
    if (totalAssets?.count) {
      return totalAssets?.count > assets?.length;
    }
    return false;
  }, [assets, totalAssets]);

  const loadMoreAssets = () => {
    if (assets.length < (totalAssets?.count ?? 0)) {
      setPage((prev) => prev + 1);
    }
  };

  const showErrorSummary = formik.submitCount > 0 && Object.keys(formik.errors).length > 0;

  return (
    <MainCard>
      <form onSubmit={formik.handleSubmit}>
        <Grid container spacing={2}>
          <Grid item xs={12} sm={9}>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <MainCard>
                  <Grid container spacing={1}>
                    <Grid item xs={12} sm={6} lg={6}>
                      <InputLabel sx={{ mb: 1 }}>Product Name</InputLabel>
                      <TextField
                        placeholder="Enter product name"
                        fullWidth
                        name="name"
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        value={formik.values.name}
                        error={formik.touched.name && Boolean(formik.errors.name)}
                        helperText={Boolean(formik.errors.name) && formik.touched.name && formik.errors.name}
                        sx={{
                          '& input': {
                            padding: '10px 18px'
                          }
                        }}
                      />
                    </Grid>

                    <Grid item xs={12} md={6}>
                      <InputLabel sx={{ mb: 1 }}>Category</InputLabel>
                      <Autocomplete
                        freeSolo
                        options={collectionOptions}
                        getOptionLabel={(option: AutoCompleteOption | string) => (typeof option === 'string' ? option : option.label)}
                        loading={collectionLoading}
                        onInputChange={(event, newInputValue) => {
                          setCollectionInput(newInputValue);
                        }}
                        value={collectionOptions.find((value) => value.value === formik.values.collectionId)}
                        onChange={(e, newValue: AutoCompleteOption | string | null) => {
                          formik.setFieldValue('collectionId', typeof newValue === 'string' ? newValue : (newValue?.value ?? ''));
                        }}
                        renderInput={(params) => (
                          <TextField
                            {...params}
                            label="Search"
                            InputProps={{
                              ...params.InputProps,
                              endAdornment: (
                                <>
                                  {collectionLoading ? <CircularProgress color="inherit" size={20} /> : null}
                                  {params.InputProps.endAdornment}
                                </>
                              )
                            }}
                            onBlur={formik.handleBlur}
                            error={formik.touched.collectionId && Boolean(formik.errors.collectionId)}
                            name="collectionId"
                          />
                        )}
                      />
                      {formik.errors.collectionId && formik.touched.collectionId && (
                        <FormHelperText error>{formik.errors.collectionId}</FormHelperText>
                      )}
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <InputLabel sx={{ mt: 1, ml: 1 }}>Is Gift Wrap Available?</InputLabel>
                      <RadioGroup
                        row
                        name="isGiftWrapAvailable"
                        value={formik.values.isGiftWrapAvailable ? 'yes' : 'no'}
                        onChange={(e) => {
                          const value = e.target.value === 'yes';
                          formik.setFieldValue('isGiftWrapAvailable', value);
                          if (!value) {
                            formik.setFieldValue('isGiftWrapCharge', '');
                          }
                        }}
                        onBlur={formik.handleBlur}
                        sx={{ ml: 2 }}
                      >
                        <FormControlLabel value="yes" control={<Radio />} label="Yes" />
                        <FormControlLabel value="no" control={<Radio />} label="No" />
                      </RadioGroup>

                      {formik.touched.isGiftWrapAvailable && Boolean(formik.errors.isGiftWrapAvailable) && (
                        <FormHelperText error>{formik.errors.isGiftWrapAvailable}</FormHelperText>
                      )}

                      {formik.values.isGiftWrapAvailable && (
                        <Box mt={2} ml={2}>
                          <TextField
                            fullWidth
                            type="number"
                            label="Gift Wrap Charge"
                            name="isGiftWrapCharge"
                            value={formik.values.isGiftWrapCharge}
                            onChange={(e) => {
                              const { value } = e.target;
                              formik.setFieldValue('isGiftWrapCharge', value === '' ? '' : Number(value));
                            }}
                            onBlur={formik.handleBlur}
                            error={formik.touched.isGiftWrapCharge && Boolean(formik.errors.isGiftWrapCharge)}
                            helperText={formik.touched.isGiftWrapCharge && formik.errors.isGiftWrapCharge}
                            sx={{
                              '& input[type=number]': {
                                '-moz-appearance': 'textfield'
                              },
                              '& input[type=number]::-webkit-outer-spin-button': {
                                '-webkit-appearance': 'none',
                                margin: 0
                              },
                              '& input[type=number]::-webkit-inner-spin-button': {
                                '-webkit-appearance': 'none',
                                margin: 0
                              },
                              '& input': {
                                padding: '10px 18px'
                              }
                            }}
                          />
                        </Box>
                      )}
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <InputLabel sx={{ mb: 1 }}>Average Weight (kg)</InputLabel>
                      <TextField
                        fullWidth
                        type="number"
                        placeholder="Enter average weight"
                        name="averageWeight"
                        value={formik.values.averageWeight ?? ''}
                        onChange={(e) => {
                          const { value } = e.target;
                          formik.setFieldValue('averageWeight', value === '' ? null : Number(value));
                        }}
                        onBlur={formik.handleBlur}
                        error={formik.touched.averageWeight && Boolean(formik.errors.averageWeight)}
                        helperText={formik.touched.averageWeight && formik.errors.averageWeight}
                        sx={{
                          '& input[type=number]': {
                            '-moz-appearance': 'textfield'
                          },
                          '& input[type=number]::-webkit-outer-spin-button': {
                            '-webkit-appearance': 'none',
                            margin: 0
                          },
                          '& input[type=number]::-webkit-inner-spin-button': {
                            '-webkit-appearance': 'none',
                            margin: 0
                          },
                          '& input': {
                            padding: '10px 18px'
                          }
                        }}
                      />
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <InputLabel sx={{ mb: 1 }}>Tax Category</InputLabel>
                      <Autocomplete
                        freeSolo
                        options={taxCategoryOptions}
                        getOptionLabel={(option: AutoCompleteOption | string) => (typeof option === 'string' ? option : option.label)}
                        loading={taxCategoryLoading}
                        inputValue={
                          typeof formik.values.taxCategoryId === 'string'
                            ? (taxCategoryOptions.find((opt) => opt.value === formik.values.taxCategoryId)?.label ??
                              formik.values.taxCategoryId)
                            : ''
                        }
                        value={taxCategoryOptions.find((opt) => opt.value === formik.values.taxCategoryId) ?? null}
                        onInputChange={(event, newInputValue) => {
                          setTaxCategoryInput(newInputValue);
                        }}
                        onChange={(e, newValue: AutoCompleteOption | string | null) => {
                          formik.setFieldValue('taxCategoryId', typeof newValue === 'string' ? newValue : (newValue?.value ?? ''));
                        }}
                        renderInput={(params) => (
                          <TextField
                            {...params}
                            label="Search"
                            InputProps={{
                              ...params.InputProps,
                              endAdornment: (
                                <>
                                  {taxCategoryLoading ? <CircularProgress color="inherit" size={20} /> : null}
                                  {params.InputProps.endAdornment}
                                </>
                              )
                            }}
                            onBlur={formik.handleBlur}
                            error={formik.touched.taxCategoryId && Boolean(formik.errors.taxCategoryId)}
                            name="taxCategoryId"
                          />
                        )}
                      />
                      {formik.errors.taxCategoryId && formik.touched.taxCategoryId && (
                        <FormHelperText error>{formik.errors.taxCategoryId}</FormHelperText>
                      )}
                    </Grid>
                    <Grid item xs={12} sm={6} lg={6}>
                      <InputLabel sx={{ mb: 1 }}>Turnaround Time (days)</InputLabel>
                      <TextField
                        fullWidth
                        type="number"
                        placeholder="Enter turnaround time in days"
                        name="turnAroundTime"
                        value={formik.values.turnAroundTime || ''}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        error={formik.touched.turnAroundTime && Boolean(formik.errors.turnAroundTime)}
                        helperText={formik.touched.turnAroundTime && formik.errors.turnAroundTime}
                        inputProps={{
                          min: 0
                        }}
                        onKeyDown={(e) => {
                          if (['-', '+', 'e', 'E'].includes(e.key)) {
                            e.preventDefault();
                          }
                        }}
                        sx={{
                          '& input[type=number]': {
                            '-moz-appearance': 'textfield'
                          },
                          '& input[type=number]::-webkit-outer-spin-button': {
                            '-webkit-appearance': 'none',
                            margin: 0
                          },
                          '& input[type=number]::-webkit-inner-spin-button': {
                            '-webkit-appearance': 'none',
                            margin: 0
                          },
                          '& input': {
                            padding: '10px 18px'
                          }
                        }}
                      />
                    </Grid>

                    <Grid item xs={12}>
                      <InputLabel sx={{ mb: 1 }}>Product caption</InputLabel>
                      <TextField
                        placeholder="Product caption"
                        fullWidth
                        multiline
                        minRows={3}
                        name="description"
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        value={formik.values.description}
                        error={formik.touched.description && Boolean(formik.errors.description)}
                        helperText={Boolean(formik.errors.description) && formik.touched.description && formik.errors.description}
                      />
                    </Grid>
                  </Grid>
                </MainCard>
              </Grid>
              <Grid item xs={12}>
                <ProductAsset
                  assets={assets ?? []}
                  openGallery={openGallery}
                  toggleGallery={() => {
                    setOpenGallery(!openGallery);
                  }}
                  handleFileChange={(e) => {
                    const files = e.target.files;
                    if (files) {
                      handleFileChange(files[0]);
                    }
                  }}
                  isAssetUploading={isAssetUploading}
                  setSelectedAssets={setSelectedAssets}
                  selectedAssets={selectedAssets}
                  handleFeatureAssetChange={(id: string) => {
                    formik.setFieldValue('featuredAssetId', id);
                  }}
                  featuredAssetId={formik.values.featuredAssetId}
                  hasMore={hasMore}
                  loadMoreAssets={loadMoreAssets}
                  assetsCount={totalAssets?.count ?? 0}
                />
              </Grid>

              <Grid item xs={12}>
                <ProductCustomizationForm formik={formik} />
              </Grid>
              <Grid item xs={12}>
                <ProductMetaForm formik={formik} />
              </Grid>
              <Grid item xs={12}>
                <ProductHighlights formik={formik} />
              </Grid>

              <Grid item xs={12}>
                <ProductLegalForm formik={formik} />
              </Grid>
              <Grid item xs={12}>
                <MainCard title="Product Variants" content={false} sx={{ padding: 0, margin: 0 }}>
                  {hasFeature('product-variants') ? (
                    <ProductVariantsTable
                      productVariants={productVariants}
                      refetch={refetch}
                      hideTitle={true}
                      callback={`/products/edit/${productId}`}
                    />
                  ) : (
                    <Box
                      sx={{
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        justifyContent: 'center',
                        py: 4,
                        gap: 2,
                        textAlign: 'center'
                      }}
                    >
                      <Typography variant="h6" color="text.secondary">
                        You don’t have access to this feature. Upgrade your plan to enable <strong>Product Variants</strong>.
                      </Typography>
                      <Button
                        variant="contained"
                        sx={{ backgroundColor: '#9E3393', '&:hover': { backgroundColor: '#7C276F' } }}
                        onClick={() => (window.location.href = '/account/subscription')}
                      >
                        Upgrade Plan
                      </Button>
                    </Box>
                  )}
                </MainCard>
              </Grid>
            </Grid>
          </Grid>
          <Grid item xs={12} sm={3}>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <MainCard title="Product Status">
                  <Box>
                    <FormControlLabel
                      control={<Switch inputProps={{ 'aria-label': 'Enabled switch' }} defaultChecked checked={formik.values.enabled} />}
                      label="Enabled"
                      name="enabled"
                      onChange={() => {
                        formik.setFieldValue('enabled', !formik.values.enabled);
                      }}
                    />
                  </Box>
                </MainCard>
              </Grid>
              <Grid item xs={12}>
                <MainCard title="Tag">
                  <Box>
                    <InputLabel sx={{ mb: 1 }}>Tag</InputLabel>
                    <Autocomplete
                      freeSolo
                      multiple
                      options={facetOptions}
                      getOptionLabel={(option: AutoCompleteOption | string) => (typeof option === 'string' ? option : option.label)}
                      loading={facetLoading}
                      onInputChange={(event, newInputValue) => {
                        setFacetInput(newInputValue);
                      }}
                      onChange={(e, newValue: (string | AutoCompleteOption)[]) => {
                        formik.setFieldValue(
                          'facets',
                          newValue.map((item) => (typeof item === 'string' ? item : item.value))
                        );
                      }}
                      value={facetOptions.filter((opt) => formik.values.facets.includes(opt.value))}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          label="Search"
                          InputProps={{
                            ...params.InputProps,
                            endAdornment: (
                              <>
                                {facetLoading ? <CircularProgress color="inherit" size={20} /> : null}
                                {params.InputProps.endAdornment}
                              </>
                            )
                          }}
                          onBlur={formik.handleBlur}
                          error={formik.touched.facets && Boolean(formik.errors.facets)}
                          name="facets"
                        />
                      )}
                    />
                    {Boolean(formik.errors.facets) && formik.touched.facets && formik.errors.facets && (
                      <FormHelperText error>{formik.errors.facets}</FormHelperText>
                    )}
                  </Box>
                </MainCard>
              </Grid>
            </Grid>
          </Grid>
        </Grid>
        <CardActions sx={{ justifyContent: 'flex-end' }}>
          <Tooltip
            title={
              showErrorSummary ? (
                <Box>
                  <Typography fontWeight="bold">Please correct the following errors:</Typography>
                  <ul style={{ marginTop: 8, paddingLeft: 20 }}>
                    {Object.entries(formik.errors).map(([field, error]) => (
                      <li key={field}>{error as ReactNode}</li>
                    ))}
                  </ul>
                </Box>
              ) : (
                'Submit'
              )
            }
          >
            <LoadingButton size="large" variant="outlined" type="submit" loading={productCreateLoading} disabled={!formik?.isValid}>
              Submit
            </LoadingButton>
          </Tooltip>
          <Button
            variant="outlined"
            color="error"
            size="large"
            onClick={() => {
              formik.resetForm();
              router.push('/products');
            }}
          >
            Cancel
          </Button>
        </CardActions>
      </form>
    </MainCard>
  );
}

export default EditProduct;
