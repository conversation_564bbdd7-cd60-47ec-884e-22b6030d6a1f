'use client';
import { Typo<PERSON>, Grid, <PERSON>ack, Card, CardContent, Box, Button } from '@mui/material';
import AnimateButton from 'components/@extended/AnimateButton';

import Image from 'next/image';

export default function WelcomePage() {
  return (
    <Stack
      spacing={2}
      alignItems="center"
      justifyContent="center"
      sx={{
        px: 2,
        mb: { xs: -0.5, sm: 0.5 },
        minHeight: {
          xs: '80vh', // small screens
          sm: '70vh',
          md: '100vh' // desktops
        }
      }}
    >
      <Card
        sx={{
          width: '100%',
          maxWidth: 939,
          p: { xs: 2, sm: 4 },
          borderRadius: 2
        }}
      >
        <CardContent>
          <Grid container spacing={2}>
            <Grid item xs={12} display="flex" justifyContent="center" mt={3}>
              <Image src="/assets/images/auth/success.svg" alt="Success" height={150} width={150} />
            </Grid>
            <Grid item xs={12}>
              <Stack direction="row" justifyContent="center" alignItems="center">
                <Box>
                  <Typography
                    variant="h2"
                    sx={{
                      textAlign: { xs: 'center', sm: 'left' },
                      fontSize: { xs: '30px', sm: '40px' }
                    }}
                  >
                    Thank You For Registering{' '}
                  </Typography>
                </Box>
              </Stack>
            </Grid>

            <Grid item xs={12} display="flex" justifyContent="center" mt={2} mb={2}>
              <Typography
                color="secondary"
                textAlign="center"
                variant="body1"
                sx={{ maxWidth: '90%', fontSize: { xs: '14px', sm: '22px' } }}
              >
                We’re excited to have you on board! As a seller, you now have access to a powerful platform to showcase your products,
                connect with customers, and grow your business.
              </Typography>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      <Stack direction="row" justifyContent="end" alignItems="end" spacing={2} sx={{ width: '100%', maxWidth: 939, p: 2 }}>
        <AnimateButton>
          <Button sx={{ width: { xs: '100%', sm: 200 }, height: 40 }} type="button" variant="contained" color="primary" href="/login">
            Continue{' '}
          </Button>
        </AnimateButton>
      </Stack>
    </Stack>
  );
}
