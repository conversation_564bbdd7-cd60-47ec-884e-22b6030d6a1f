'use client';

import { useState, useEffect, useMemo, MouseEvent, useCallback } from 'react';
import { useRouter } from 'next/navigation';

import { useTheme } from '@mui/material/styles';
import Stack from '@mui/material/Stack';
import Tooltip from '@mui/material/Tooltip';
import Typography from '@mui/material/Typography';

import { SortingState, ColumnFiltersState, ColumnDef, PaginationState } from '@tanstack/react-table';

import { Add, Edit, Eye, Trash } from 'iconsax-react';
import IconButton from '../../components/@extended/IconButton';
import IndeterminateCheckbox from '../../components/third-party/react-table/IndeterminateCheckbox';
import { InventoryTable } from './InventoryTable';
import withPermission from 'hoc/withPermission';
import { InventoryItem } from 'redux/app/types/inventory.type';
import { PermissionKeys } from 'enums/permission-key.enum';
import { convertFiltersToLoopbackWhere, convertPaginationToLoopback, convertSortingToLoopbackSort } from 'utils/table-filter';
import { useAuth } from 'contexts/AuthContext';
import AlertInventoryDelete from './InventoryDelete';
import { useGetInventoryItemsQuery, useLazyGetInventoryItemsCountQuery } from 'redux/app/inventory/inventoryApiSlice';
import Image from 'next/image';

const InventoryListPage = () => {
  const theme = useTheme();
  const router = useRouter();
  const { hasPermission } = useAuth();

  const [sorting, setSorting] = useState<SortingState>([{ id: 'createdOn', desc: true }]);
  const [filters, setFilters] = useState<ColumnFiltersState>([]);
  const [globalFilter, setGlobalFilter] = useState('');
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10
  });

  const [deleteId, setDeleteId] = useState('');
  const [deleteTitle, setDeleteTitle] = useState('');
  const [open, setOpen] = useState(false);

  const canEdit = hasPermission(PermissionKeys.UpdateInventory);
  const canDelete = hasPermission(PermissionKeys.DeleteInventory);

  const {
    data: inventoryList,
    isLoading,
    refetch
  } = useGetInventoryItemsQuery({
    order: convertSortingToLoopbackSort(sorting),
    where: convertFiltersToLoopbackWhere(filters, '', ['productVariant.name']),
    ...convertPaginationToLoopback(pagination),
    include: [
      {
        relation: 'productVariant',
        scope: {
          ...(globalFilter && {
            where: {
              name: { ilike: `%${globalFilter}%` }
            }
          }),
          include: [
            {
              relation: 'featuredAsset'
            }
          ]
        }
      }
    ]
  });

  const [fetchCount, { data: inventoryCount }] = useLazyGetInventoryItemsCountQuery();

  useEffect(() => {
    fetchCount({});
  }, [fetchCount]);

  useEffect(() => {
    refetch();
  }, [refetch]);

  const handleClose = useCallback(() => {
    setOpen(false);
  }, []);

  const columns = useMemo<ColumnDef<InventoryItem>[]>(
    () => [
      {
        id: 'Row Selection',
        header: ({ table }) => (
          <IndeterminateCheckbox
            {...{
              checked: table.getIsAllRowsSelected(),
              indeterminate: table.getIsSomeRowsSelected(),
              onChange: table.getToggleAllRowsSelectedHandler()
            }}
          />
        ),
        cell: ({ row }) => (
          <IndeterminateCheckbox
            {...{
              checked: row.getIsSelected(),
              disabled: !row.getCanSelect(),
              indeterminate: row.getIsSomeSelected(),
              onChange: row.getToggleSelectedHandler()
            }}
          />
        )
      },
      {
        header: 'Product Variant',
        accessorKey: 'productVariant',
        enableSorting: false,
        cell: ({ row }) => {
          const imageUrl = row.original?.productVariant?.featuredAsset?.previewUrl;
          const name = row.original?.productVariant?.name;

          return (
            <div style={{ display: 'flex', alignItems: 'center' }}>
              {imageUrl && (
                <Image
                  src={imageUrl}
                  alt={name || 'Product'}
                  width={40}
                  height={40}
                  style={{
                    objectFit: 'contain',
                    borderRadius: 4,
                    marginRight: 8
                  }}
                />
              )}
              <Typography>{name || '-'}</Typography>
            </div>
          );
        }
      },

      {
        header: 'Warehouse',
        accessorKey: 'warehouse',
        enableSorting: false,
        cell: ({ row }) => <Typography>{row.original.warehouse?.name}</Typography>
      },
      {
        header: 'Stock On Hand',
        accessorKey: 'stockOnHand',
        enableSorting: false,
        cell: ({ row }) => <Typography>{row.original.stockOnHand}</Typography>
      },
      {
        header: 'Stock Allocated',
        accessorKey: 'stockAllocated',
        enableSorting: false,
        cell: ({ row }) => <Typography>{row.original.stockAllocated}</Typography>
      },
      {
        header: 'Actions',
        enableSorting: false,
        cell: ({ row }: any) => {
          const collapseIcon =
            row.getCanExpand() && row.getIsExpanded() ? (
              <Add
                style={{
                  color: theme.palette.error.main,
                  transform: 'rotate(45deg)'
                }}
              />
            ) : (
              <Eye />
            );

          return (
            <Stack direction="row" spacing={0} justifyContent="flex-start">
              <Tooltip title="View">
                <IconButton color="secondary" onClick={row.getToggleExpandedHandler()}>
                  {collapseIcon}
                </IconButton>
              </Tooltip>
              <Tooltip title="Edit">
                <IconButton
                  color="primary"
                  onClick={(e: MouseEvent) => {
                    e.stopPropagation();
                    router.push(`/inventory/edit/${row.original.id}`);
                  }}
                  disabled={!canEdit}
                >
                  <Edit />
                </IconButton>
              </Tooltip>
              <Tooltip title="Delete">
                <IconButton
                  color="error"
                  onClick={(e: MouseEvent) => {
                    e.stopPropagation();
                    setOpen(true);
                    setDeleteId(row.original.id);
                    setDeleteTitle(row.original.sku);
                  }}
                  disabled={!canDelete}
                >
                  <Trash />
                </IconButton>
              </Tooltip>
            </Stack>
          );
        }
      }
    ],
    [theme, router, canEdit, canDelete]
  );

  return (
    <>
      <InventoryTable
        data={inventoryList || []}
        columns={columns}
        setSorting={setSorting}
        sorting={sorting}
        filters={filters}
        setFilters={setFilters}
        loading={isLoading}
        globalFilter={globalFilter}
        setGlobalFilter={setGlobalFilter}
        pagination={pagination}
        setPagination={setPagination}
        totalRows={(inventoryCount?.count as number) ?? 0}
        refetch={refetch}
      />
      <AlertInventoryDelete id={deleteId} title={deleteTitle} open={open} handleClose={handleClose} refetch={refetch} />
    </>
  );
};

export default withPermission(PermissionKeys.ViewInventory)(InventoryListPage);
