'use client';

import { useMemo, useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { useTheme } from '@mui/material/styles';
import Stack from '@mui/material/Stack';
import Tooltip from '@mui/material/Tooltip';
import Typography from '@mui/material/Typography';
import { SortingState, ColumnFiltersState, ColumnDef, PaginationState } from '@tanstack/react-table';
import IconButton from '../../components/@extended/IconButton';
import { Add, Eye } from 'iconsax-react';
import { ThemeMode } from 'config';
import IndeterminateCheckbox from 'components/third-party/react-table/IndeterminateCheckbox';
import OrderTable from './OrderTable';
import { OrderLineItem } from 'types/order';

const OrderListPage = ({
  data,
  loading,
  totalRows,
  refetch,
  sorting,
  setSorting,
  pagination,
  setPagination,
  columnFilters,
  setColumnFilters,
  globalFilter,
  setGlobalFilter
}: {
  data: OrderLineItem[];
  loading: boolean;
  refetch: () => void;
  totalRows: number;

  sorting: SortingState;
  setSorting: React.Dispatch<React.SetStateAction<SortingState>>;
  pagination: PaginationState;
  setPagination: React.Dispatch<React.SetStateAction<PaginationState>>;
  columnFilters: ColumnFiltersState;
  setColumnFilters: React.Dispatch<React.SetStateAction<ColumnFiltersState>>;
  globalFilter: string;
  setGlobalFilter: React.Dispatch<React.SetStateAction<string>>;
}) => {
  const theme = useTheme();
  const router = useRouter();
  const [, setOpen] = useState<boolean>(false);
  const handleClose = useCallback(() => setOpen((prev) => !prev), []);
  const columns = useMemo<ColumnDef<any>[]>(
    () => [
      {
        id: 'Row Selection',
        header: ({ table }) => (
          <IndeterminateCheckbox
            checked={table.getIsAllRowsSelected()}
            indeterminate={table.getIsSomeRowsSelected()}
            onChange={table.getToggleAllRowsSelectedHandler()}
          />
        ),
        cell: ({ row }) => (
          <IndeterminateCheckbox
            checked={row.getIsSelected()}
            disabled={!row.getCanSelect()}
            indeterminate={row.getIsSomeSelected()}
            onChange={row.getToggleSelectedHandler()}
          />
        )
      },
      {
        header: 'image',
        accessorKey: 'featuredAsset',
        cell: ({ row }) => {
          const imageUrl = row.original?.productVariant.featuredAsset?.previewUrl;
          const productId = row.original?.productId;

          return (
            <div style={{ display: 'flex', alignItems: 'center' }}>
              {imageUrl ? (
                <img
                  src={imageUrl}
                  alt="Product"
                  style={{
                    width: 50,
                    height: 50,
                    objectFit: 'contain',
                    borderRadius: 4,
                    marginRight: 26
                  }}
                />
              ) : (
                <span>-</span>
              )}
              <span>{productId}</span>
            </div>
          );
        },
        meta: { className: 'cell-center' }
      },

      {
        header: 'Product Name',
        accessorKey: 'productVariant.name',
        enableSorting: true,
        cell: ({ row }) => <Typography> {row.original?.productVariant?.name ?? '-'}</Typography>
      },
      {
        header: 'Order ID',
        accessorKey: 'orderId ',
        enableSorting: true,
        cell: ({ row }) => <Typography>{row.original?.order?.orderId ?? '-'}</Typography>
      },
      {
        header: 'Order Date',
        accessorKey: 'createdOn',
        enableSorting: true,
        cell: ({ row }) => (
          <Typography>
            {row.original?.createdOn
              ? new Date(row.original.createdOn).toLocaleDateString('en-IN', {
                  day: '2-digit',
                  month: 'short',
                  year: 'numeric'
                })
              : '-'}
          </Typography>
        )
      },

      {
        header: 'Status',
        accessorKey: 'order.status',
        enableSorting: true,
        cell: ({ row }) => <Typography>{row.original?.status ?? '-'}</Typography>
      },

      {
        header: 'Quantity',
        accessorKey: 'quantity',
        enableSorting: true,
        cell: ({ row }) => <Typography> {row.original?.quantity ?? '-'} Nos.</Typography>
      },
      {
        header: 'Actions',
        enableSorting: false,
        cell: ({ row }) => {
          const collapseIcon =
            row.getCanExpand() && row.getIsExpanded() ? (
              <Add
                style={{
                  color: theme.palette.error.main,
                  transform: 'rotate(45deg)'
                }}
              />
            ) : (
              <Eye />
            );
          return (
            <Stack direction="row" alignItems="center" justifyContent="center" spacing={0}>
              <Tooltip title="View">
                <IconButton
                  onClick={row.getToggleExpandedHandler()}
                  sx={{
                    color: theme.palette.primary.light,
                    ':hover': {
                      backgroundColor: theme.palette.mode === ThemeMode.DARK ? theme.palette.common.white : theme.palette.primary.light
                    }
                  }}
                >
                  {collapseIcon}
                </IconButton>
              </Tooltip>
            </Stack>
          );
        }
      }
    ],
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [theme, router, handleClose]
  );

  return (
    <>
      <OrderTable
        {...{
          data,
          columns,
          setSorting,
          sorting,
          columnFilters,
          setColumnFilters,
          loading,
          globalFilter,
          setGlobalFilter,
          pagination,
          setPagination,
          totalRows,
          refetch
        }}
      />
    </>
  );
};

export default OrderListPage;
