'use client';

import { useState, cloneElement, ReactElement } from 'react';

// next
import Link from 'next/link';

// material-ui
import { alpha, useTheme } from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';
import AppBar from '@mui/material/AppBar';
import Container from '@mui/material/Container';
import Drawer from '@mui/material/Drawer';
import Links from '@mui/material/Link';
import List from '@mui/material/List';
import ListItemButton from '@mui/material/ListItemButton';
import ListItemText from '@mui/material/ListItemText';
import Stack from '@mui/material/Stack';
import Toolbar from '@mui/material/Toolbar';
import useScrollTrigger from '@mui/material/useScrollTrigger';
import Box from '@mui/material/Box';

// project-imports
import IconButton from 'components/@extended/IconButton';
import Logo from 'components/logo';

// assets
import { HambergerMenu } from 'iconsax-react';
import { Grid } from '@mui/material';
import Avatar from 'components/@extended/Avatar';
import { usePathname } from 'next/navigation';
interface ElevationScrollProps {
  children: ReactElement;
  window?: Window | Node;
}

// elevation scroll
function ElevationScroll({ children, window }: ElevationScrollProps) {
  const theme = useTheme();
  const trigger = useScrollTrigger({
    disableHysteresis: true,
    threshold: 10,
    target: window ? window : undefined
  });

  return cloneElement(children, {
    style: {
      boxShadow: trigger ? '0 8px 6px -10px rgba(0, 0, 0, 0.5)' : 'none',
      backgroundColor: trigger ? alpha(theme.palette.background.default, 0.8) : alpha(theme.palette.background.default, 0.1)
    }
  });
}

// ==============================|| COMPONENTS - APP BAR ||============================== //

export default function Header() {
  const theme = useTheme();

  const matchDownMd = useMediaQuery(theme.breakpoints.down('md'));
  const [drawerToggle, setDrawerToggle] = useState<boolean>(false);
  const path = usePathname();

  /** Method called on multiple components with different event types */
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const drawerToggler = (open: boolean) => (event: any) => {
    if (event.type! === 'keydown' && (event.key! === 'Tab' || event.key! === 'Shift')) {
      return;
    }
    setDrawerToggle(open);
  };

  const avatar1 = '/assets/images/users/girl.png';

  const linksSx = {
    textDecoration: 'none'
  };

  const links = [
    {
      name: 'Sell on Ecomdukes',
      link: '/sell-on-ecomdukes'
    },
    {
      name: 'Plans & Pricing',
      link: '/pricing'
    },
    {
      name: 'Profit Calculator',
      link: '/profit-calculator'
    }
  ];

  return (
    <ElevationScroll>
      <AppBar
        sx={{
          bgcolor: alpha(theme.palette.background.default, 0.1),
          backdropFilter: 'blur(8px)',
          color: theme.palette.text.primary,
          boxShadow: 'none'
        }}
      >
        <Container maxWidth="xl" disableGutters={matchDownMd}>
          <Toolbar sx={{ px: { xs: 1.5, sm: 4, md: 0, lg: 0 }, py: 1 }}>
            <Stack direction="row" sx={{ flexGrow: 1, display: { xs: 'none', md: 'block' }, ml: '100px' }} alignItems="center">
              <Box sx={{ display: 'inline-block' }}>
                <Logo reverse to="/" sx={{ width: '150px' }} />
              </Box>
            </Stack>
            <Stack
              direction="row"
              alignItems="center"
              sx={{
                '& .header-link': {
                  fontWeight: 500,
                  '&:hover': { color: theme.palette.primary.main }
                },
                display: { xs: 'none', md: 'flex' },
                mr: '100px'
              }}
              spacing={5}
            >
              {links.map((item) => (
                <Links
                  key={item.name}
                  className="header-link"
                  sx={{
                    position: 'relative',
                    textDecoration: 'none',
                    fontWeight: 500,
                    '&::after':
                      path === item.link
                        ? {
                            content: '""',
                            position: 'absolute',
                            left: 0,
                            bottom: -4,
                            width: '100%',
                            height: '3px',
                            backgroundColor: '#00004F'
                          }
                        : ''
                  }}
                  color="#00004F"
                  component={Link}
                  href={item.link}
                  underline="none"
                >
                  {item.name}
                </Links>
              ))}

              <Box sx={{ display: { xs: 'none', md: 'inline-block' } }}>
                <Grid item>
                  <Stack direction="row" spacing={1.25} alignItems="center">
                    <Avatar alt="profile user" src={avatar1} />
                  </Stack>
                </Grid>
              </Box>
            </Stack>
            <Box
              sx={{
                width: '100%',
                alignItems: 'center',
                justifyContent: 'space-between',
                display: { xs: 'flex', md: 'none' }
              }}
            >
              <Stack
                direction="row"
                alignItems="center"
                justifyContent="space-between"
                sx={{ display: { xs: 'flex', md: 'none' }, width: '100%' }}
              >
                <Logo reverse to="/" sx={{ width: '120px' }} />
                <IconButton size="large" color="secondary" onClick={drawerToggler(true)} sx={{ p: 1 }}>
                  <HambergerMenu />
                </IconButton>
              </Stack>

              <Drawer
                anchor="top"
                open={drawerToggle}
                onClose={drawerToggler(false)}
                sx={{ '& .MuiDrawer-paper': { backgroundImage: 'none' } }}
              >
                <Box
                  sx={{
                    width: 'auto',
                    '& .MuiListItemIcon-root': { fontSize: '1rem', minWidth: 32 }
                  }}
                  role="presentation"
                  onKeyDown={drawerToggler(false)}
                >
                  <List>
                    <List>
                      {links.map((item) => (
                        <Links key={item.name} sx={linksSx} href={item.link}>
                          <ListItemButton>
                            <ListItemText
                              primary={item.name}
                              primaryTypographyProps={{
                                variant: 'h5',
                                color: '#00004F'
                              }}
                            />
                          </ListItemButton>
                        </Links>
                      ))}
                    </List>
                  </List>
                </Box>
              </Drawer>
            </Box>
          </Toolbar>
        </Container>
      </AppBar>
    </ElevationScroll>
  );
}
