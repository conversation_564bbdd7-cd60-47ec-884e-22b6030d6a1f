import { ApiSliceIdentifier } from 'enums/api.enum';
import { apiSlice } from '../../apiSlice';
import { Asset, Product } from 'types/product';
import { Count } from 'types/api';
import { IFilter } from '../types/filter';
import { buildFilterParams } from 'utils/buildFilterParams';
import { ProductDto } from 'types/product-dto';
import { PinnedProduct, ProductVariant, ProductVariantUpdateDto } from 'types/product-variant';

export const productApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getproducts: builder.query<Product[], IFilter | void>({
      query: (filter) => ({
        url: '/products',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: buildFilterParams(filter ?? undefined)
        }
      })
    }),
    getproductById: builder.query<Product, { id: string; filter?: IFilter }>({
      query: ({ id, filter }) => ({
        url: `/products/${id}`,
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: buildFilterParams(filter ?? undefined)
        }
      })
    }),
    getproductCount: builder.query<
      Count,
      {
        where?: Record<string, unknown>;
        include?: Array<Record<string, unknown> | string>;
      }
    >({
      query: ({ where, include }) => ({
        url: '/products/count',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: JSON.stringify({
            where,
            include
          })
        }
      })
    }),
    getAssets: builder.query<Asset[], IFilter | void>({
      query: (filter) => ({
        url: '/assets',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: buildFilterParams(filter ?? undefined)
        }
      })
    }),
    createAsset: builder.mutation<void, FormData>({
      query: (formData) => ({
        url: `/assets`,
        method: 'POST',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        body: formData,
        formData: true
      })
    }),
    createProduct: builder.mutation<void, ProductDto>({
      query: (formData) => ({
        url: `/products`,
        method: 'POST',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        body: formData
      })
    }),
    deleteProduct: builder.mutation<void, string>({
      query: (id) => ({
        url: `/products/${id}`,
        method: 'DELETE',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE
      })
    }),
    deleteProductVariantById: builder.mutation<void, string>({
      query: (id) => ({
        url: `/product-variants/${id}`,
        method: 'DELETE',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE
      })
    }),
    getProductVariants: builder.query<ProductVariant[], IFilter | void>({
      query: (filter) => ({
        url: '/product-variants',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: buildFilterParams(filter ?? undefined)
        }
      })
    }),
    updateProductById: builder.mutation<void, { id: string; body: Partial<ProductDto> }>({
      query: ({ id, body }) => ({
        url: `/products/${id}`,
        method: 'PATCH',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        body
      })
    }),
    getProductVariantById: builder.query<ProductVariant, { id: string; filter?: IFilter }>({
      query: ({ filter, id }) => ({
        url: `/product-variants/${id}`,
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: buildFilterParams(filter ?? undefined)
        }
      })
    }),
    updateProductVariantById: builder.mutation<void, { id: string; body: ProductVariantUpdateDto }>({
      query: ({ body, id }) => ({
        url: `/product-variants/${id}`,
        method: 'PATCH',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        body
      })
    }),
    getAssetsCount: builder.query<Count, IFilter | void>({
      query: (filter) => ({
        url: '/assets/count',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: buildFilterParams(filter ?? undefined)
        }
      })
    }),
    createPinnedProduct: builder.mutation<PinnedProduct, { productVariantId: string }>({
      query: (body) => ({
        url: '/pinned-products',
        method: 'POST',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        body
      })
    }),

    deletePinnedProduct: builder.mutation<void, string>({
      query: (id) => ({
        url: `/pinned-products/${id}`,
        method: 'DELETE',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE
      })
    }),
    getPinnedProducts: builder.query<PinnedProduct[], any>({
      query: (filter) => ({
        url: '/pinned-products',
        method: 'GET',
        params: filter,
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE
      })
    }),
    updatePinnedProduct: builder.mutation<void, { id: string; body: Partial<PinnedProduct> }>({
      query: ({ id, body }) => ({
        url: `/pinned-products/${id}`,
        method: 'PATCH',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        body
      })
    })
  })
});

export const {
  useGetproductsQuery,
  useGetproductCountQuery,
  useGetAssetsQuery,
  useCreateAssetMutation,
  useCreateProductMutation,
  useGetproductByIdQuery,
  useDeleteProductMutation,
  useGetProductVariantsQuery,
  useUpdateProductByIdMutation,
  useGetProductVariantByIdQuery,
  useUpdateProductVariantByIdMutation,
  useLazyGetproductsQuery,
  useGetAssetsCountQuery,
  useCreatePinnedProductMutation,
  useDeletePinnedProductMutation,
  useGetPinnedProductsQuery,
  useUpdatePinnedProductMutation,
  useDeleteProductVariantByIdMutation
} = productApiSlice;
