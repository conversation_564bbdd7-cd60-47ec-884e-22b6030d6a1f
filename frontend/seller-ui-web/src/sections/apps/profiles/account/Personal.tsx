import { useEffect, useState, ChangeEvent } from 'react';
import { useFormik } from 'formik';

// material-ui
import { useTheme } from '@mui/material/styles';
import Grid from '@mui/material/Grid';
import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import FormLabel from '@mui/material/FormLabel';
import TextField from '@mui/material/TextField';
import InputLabel from '@mui/material/InputLabel';
import Typography from '@mui/material/Typography';
import OutlinedInput from '@mui/material/OutlinedInput';
import Box from '@mui/material/Box';

// project imports
import Avatar from 'components/@extended/Avatar';
import MainCard from 'components/MainCard';
import { ALLOWED_IMAGE_EXTENTIONS, ThemeMode } from 'config';

// assets
import { Camera } from 'iconsax-react';
import { personalFormValidation } from 'validation/personal.validation';
import { useGetUserQuery, useUpdateUserMutation } from '../../../../redux/auth/authApiSlice';
import { openSnackbar } from 'api/snackbar';
import { SnackbarProps } from 'types/snackbar';
import { useApiErrorHandler } from 'hooks/useApiErrorHandler';
import { InputAdornment, Select } from '@mui/material';
import { MenuItem } from '@mui/material';
import { Gender } from 'types/auth';
import { UserDto } from 'types/user-profile';
import { extractCountryCode } from 'utils/countryCodes';
import { useRouter } from 'next/navigation';

export default function Personal() {
  const theme = useTheme();
  const { data: user, refetch } = useGetUserQuery();
  const [updatePersonalDetails, { error: updateError, reset: updateReset }] = useUpdateUserMutation();
  const handleError = useApiErrorHandler();

  const [selectedImage, setSelectedImage] = useState<File | undefined>(undefined);
  const [avatar, setAvatar] = useState<string | undefined>(user?.photoUrl ?? '');
  const router = useRouter();
  const handleFileChange = (event: ChangeEvent<HTMLInputElement>, fieldName: keyof typeof formik.values) => {
    const file = event.target.files?.[0] || undefined;
    setSelectedImage(file);
    formik.setFieldValue(fieldName, file?.name || '');
    if (file) {
      setAvatar(URL.createObjectURL(file));
    }
  };

  const formik = useFormik({
    initialValues: {
      firstName: user?.firstName ?? '',
      lastName: user?.lastName ?? '',
      phoneNumber: extractCountryCode(user?.phone ?? '')?.phoneNumber ?? '',
      email: user?.email ?? '',
      designation: user?.designation ?? '',
      gender: user?.gender ? (user?.gender as Gender) : undefined,
      dob: user?.dob ?? '',
      photoUrl: user?.photoUrl ?? ''
    },
    validationSchema: personalFormValidation,
    onSubmit: async (values) => {
      const countryCode = extractCountryCode(user?.phone ?? '')?.countryCode ?? '';
      const body: Partial<UserDto> = {
        firstName: values.firstName.trim(),
        lastName: values.lastName.trim(),
        phone: `${countryCode}${values.phoneNumber}`,
        email: values.email,
        designation: values.designation,
        gender: values.gender ?? undefined,
        dob: values.dob ? new Date(values.dob) : undefined
      };

      const formData = new FormData();
      Object.entries(body).forEach(([key, value]) => {
        if (value !== undefined) {
          formData.append(key, value as string);
        }
      });
      if (selectedImage) {
        formData.append('photoUrl', selectedImage);
      }
      await updatePersonalDetails({
        userId: user?.id ?? '',
        body: formData
      }).unwrap();

      refetch();

      openSnackbar({
        open: true,
        message: 'Profile updated successfully',
        variant: 'alert',
        alert: { color: 'success' }
      } as SnackbarProps);
      setSelectedImage(undefined);
      router.push('/account/profile');
    }
  });

  useEffect(() => {
    if (updateError) {
      handleError(updateError);
      updateReset();
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [handleError, updateError]);

  return (
    <form onSubmit={formik.handleSubmit}>
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <MainCard title="Personal Information">
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <Stack spacing={2.5} alignItems="center" sx={{ m: 3 }}>
                  <FormLabel
                    htmlFor="change-avtar"
                    sx={{
                      position: 'relative',
                      borderRadius: '50%',
                      overflow: 'hidden',
                      '&:hover .MuiBox-root': { opacity: 1 },
                      cursor: 'pointer'
                    }}
                  >
                    <Avatar alt="Avatar 1" src={avatar} sx={{ width: 76, height: 76 }} />
                    <Box
                      sx={{
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        backgroundColor: theme.palette.mode === ThemeMode.DARK ? 'rgba(255, 255, 255, .75)' : 'rgba(0,0,0,.65)',
                        width: '100%',
                        height: '100%',
                        opacity: 0,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center'
                      }}
                    >
                      <Stack spacing={0.5} alignItems="center">
                        <Camera
                          style={{
                            color: theme.palette.secondary.lighter,
                            fontSize: '1.5rem'
                          }}
                        />
                        <Typography sx={{ color: 'secondary.lighter' }} variant="caption">
                          Upload
                        </Typography>
                      </Stack>
                    </Box>
                  </FormLabel>
                  <input
                    type="file"
                    id="change-avtar"
                    style={{ display: 'none' }}
                    onChange={(e) => handleFileChange(e as ChangeEvent<HTMLInputElement>, 'photoUrl')}
                    accept={ALLOWED_IMAGE_EXTENTIONS.join(', ')}
                  />
                </Stack>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Stack spacing={1}>
                  <InputLabel htmlFor="personal-first-name">First Name</InputLabel>
                  <TextField
                    fullWidth
                    id="firstName"
                    name="firstName"
                    value={formik.values.firstName}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={formik.touched.firstName && Boolean(formik.errors.firstName)}
                    helperText={formik.touched.firstName && formik.errors.firstName}
                  />
                </Stack>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Stack spacing={1}>
                  <InputLabel htmlFor="personal-last-name">Last Name</InputLabel>
                  <OutlinedInput
                    id="lastName"
                    name="lastName"
                    value={formik.values.lastName}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={formik.touched.lastName && Boolean(formik.errors.lastName)}
                  />
                  {formik.touched.lastName && formik.errors.lastName && (
                    <Typography variant="caption" color="error">
                      {formik.errors.lastName}
                    </Typography>
                  )}
                </Stack>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Stack spacing={1}>
                  <InputLabel htmlFor="phoneNumber">Phone</InputLabel>
                  <TextField
                    name="phoneNumber"
                    fullWidth
                    variant="outlined"
                    value={formik.values.phoneNumber}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={formik.touched.phoneNumber && Boolean(formik.errors.phoneNumber)}
                    sx={{ borderRadius: '20px' }}
                    inputProps={{
                      maxLength: 10
                    }}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">{`+${extractCountryCode(user?.phone ?? '')?.countryCode ?? ''}`}</InputAdornment>
                      )
                    }}
                  />
                  {formik.touched.phoneNumber && formik.errors.phoneNumber && (
                    <Typography variant="caption" color="error">
                      {formik.errors.phoneNumber}
                    </Typography>
                  )}
                </Stack>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Stack spacing={1}>
                  <InputLabel htmlFor="email">Email</InputLabel>
                  <OutlinedInput
                    id="email"
                    name="email"
                    value={formik.values.email}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={formik.touched.email && Boolean(formik.errors.email)}
                  />
                  {formik.touched.email && formik.errors.email && (
                    <Typography variant="caption" color="error">
                      {formik.errors.email}
                    </Typography>
                  )}
                </Stack>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Stack spacing={1}>
                  <InputLabel htmlFor="designation">Designation</InputLabel>
                  <OutlinedInput
                    id="designation"
                    name="designation"
                    value={formik.values.designation}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={formik.touched.designation && Boolean(formik.errors.designation)}
                  />
                  {formik.touched.designation && formik.errors.designation && (
                    <Typography variant="caption" color="error">
                      {formik.errors.designation}
                    </Typography>
                  )}
                </Stack>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Stack spacing={1}>
                  <InputLabel htmlFor="gender">Gender</InputLabel>
                  <Select
                    name="gender"
                    value={formik.values.gender}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={formik.touched.gender && Boolean(formik.errors.gender)}
                    fullWidth
                    displayEmpty
                    sx={{ borderRadius: '20px' }}
                  >
                    <MenuItem disabled value={undefined}>
                      Select Gender
                    </MenuItem>
                    <MenuItem value={Gender.Male}>Male</MenuItem>
                    <MenuItem value={Gender.Female}>Female</MenuItem>
                    <MenuItem value={Gender.Other}>Others</MenuItem>
                  </Select>
                  {formik.touched.gender && typeof formik.errors.gender === 'string' && (
                    <Typography color="error" variant="caption">
                      {formik.errors.gender}
                    </Typography>
                  )}
                </Stack>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Stack spacing={1}>
                  <InputLabel htmlFor="dob"> Date of Birth</InputLabel>
                  <Grid sx={{ display: 'flex' }}>
                    <TextField
                      fullWidth
                      type="date"
                      name="dob"
                      value={formik.values.dob}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      error={formik.touched.dob && Boolean(formik.errors.dob)}
                      helperText={formik.touched.dob && formik.errors.dob}
                      InputLabelProps={{ shrink: true }}
                    />
                  </Grid>
                </Stack>
              </Grid>
            </Grid>
          </MainCard>
        </Grid>
        <Grid item xs={12}>
          <Stack direction="row" justifyContent="flex-end" alignItems="center" spacing={2}>
            <Button type="submit" variant="contained">
              Update Profile
            </Button>
          </Stack>
        </Grid>
      </Grid>
    </form>
  );
}
