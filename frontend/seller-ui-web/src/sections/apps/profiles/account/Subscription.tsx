'use client';

import { useEffect, useState } from 'react';
import { useTheme } from '@mui/material/styles';
import Chip from '@mui/material/Chip';
import Grid from '@mui/material/Grid';
import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import ListItemText from '@mui/material/ListItemText';
import ListItemIcon from '@mui/material/ListItemIcon';
import Stack from '@mui/material/Stack';
import Radio from '@mui/material/Radio';
import RadioGroup from '@mui/material/RadioGroup';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import { ShieldCross, TickSquare } from 'iconsax-react';
import MainCard from 'components/MainCard';
import { Button, Dialog, DialogActions, DialogContent, DialogTitle } from '@mui/material';
import {
  useCreateSubscriptionMutation,
  useGetFeaturesQuery,
  useGetPlansQuery,
  useGetSubscriptionQuery
} from '../../../../redux/ecom/ecomApiSlice';
import { useGetUserQuery } from '../../../../redux/auth/authApiSlice';
import { useApiErrorHandler } from 'hooks/useApiErrorHandler';
import { openSnackbar } from 'api/snackbar';
import { SnackbarProps } from 'types/snackbar';
import { setFeaturestoCache } from 'utils/subscription-cache';
import { useDispatch } from 'react-redux';
const planOrder = ['free-plan', 'basic-plan', 'advanced-plan'];

export default function Pricing() {
  const theme = useTheme();
  const dispatch = useDispatch();
  const { data: plans = [] } = useGetPlansQuery();
  const { data: features = [] } = useGetFeaturesQuery();
  const { data: user } = useGetUserQuery();
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState<string>(plans[0]?.key || '');
  const [pendingPlan, setPendingPlan] = useState<string | null>(null);

  const { data: subscriptions, refetch } = useGetSubscriptionQuery(user?.profileId ?? '', {
    skip: !user?.profileId
  });
  const handleError = useApiErrorHandler();

  const [createSubscriptionApi, { error: updateError, reset: updateReset }] = useCreateSubscriptionMutation();
  useEffect(() => {
    if (user?.profileId) {
      refetch();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user?.profileId, refetch]);

  const isDowngrade = (newPlanKey: string) => {
    const currentActiveSubscription = subscriptions?.find((sub) => sub.status === 'ACTIVE');
    if (!currentActiveSubscription) return false;

    const currentPlan = plans.find((p) => p.id === currentActiveSubscription.planId);
    const newPlan = plans.find((p) => p.key === newPlanKey);
    if (!currentPlan || !newPlan) return false;

    const currentIndex = planOrder.indexOf(currentPlan.key);
    const newIndex = planOrder.indexOf(newPlan.key);

    // If either plan key isn't found in the order, don't trigger downgrade modal
    if (currentIndex === -1 || newIndex === -1) return false;

    return newIndex < currentIndex;
  };

  useEffect(() => {
    if (subscriptions?.length) {
      const activeSubscription = subscriptions.find((sub) => sub.status === 'ACTIVE');
      if (activeSubscription) {
        const subscribedPlan = plans.find((plan) => plan.id === activeSubscription.planId);
        if (subscribedPlan) {
          setSelectedPlan(subscribedPlan.key);
        }
      }
    } else if (plans.length) {
      setSelectedPlan(plans[0]?.key);
    }
  }, [subscriptions, plans]);
  const handlePlanChange = (value: string) => {
    const isDowngradePlan = isDowngrade(value);

    if (isDowngradePlan) {
      setPendingPlan(value);
      setShowConfirmModal(true);
    } else {
      setSelectedPlan(value);
    }
  };

  const handleSubscription = async () => {
    if (!user?.profileId) return;

    const selectedPlanObj = plans.find((plan) => plan.key === selectedPlan);
    if (!selectedPlanObj) return;

    await createSubscriptionApi({
      subscriberId: user.profileId,
      planId: selectedPlanObj.id
    }).unwrap();
    refetch();

    openSnackbar({
      open: true,
      message: 'Plans updated successfully',
      variant: 'alert',
      alert: { color: 'success' }
    } as SnackbarProps);
  };

  useEffect(() => {
    if (!subscriptions?.length) return;
    setFeaturestoCache(subscriptions, plans, dispatch);
  }, [subscriptions, plans]);

  const currentPlan = plans.find((plan) => plan.key === selectedPlan);
  const priceSelectedPlan = {
    padding: 3,
    borderRadius: 1,
    border: '1px solid',
    borderColor: theme.palette.divider,
    bgcolor: theme.palette.info.lighter
  };

  const priceUnselectedPlan = {
    padding: 3,
    borderRadius: 1,
    border: '1px solid',
    borderColor: theme.palette.divider,
    bgcolor: theme.palette.background.paper
  };
  useEffect(() => {
    if (updateError) {
      handleError(updateError);
      updateReset();
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [handleError, updateError]);

  return (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <Grid container spacing={3}>
          <Grid item xs={12} md={6} lg={7}>
            <MainCard>
              <List component="ul">
                {features.map((feature) => {
                  const matchingFeature = currentPlan?.planFeatureValues.find(
                    (featureValue) => featureValue.featureValue?.feature?.id === feature.id
                  );

                  return (
                    <ListItem key={feature.id} divider>
                      <ListItemText primary={feature.name} />
                      <ListItemIcon>
                        {matchingFeature?.featureValue?.value === 'true' ? (
                          <TickSquare size="16" color={theme.palette.success.main} />
                        ) : matchingFeature?.featureValue?.value === 'false' ? (
                          <ShieldCross size="16" color={theme.palette.error.main} />
                        ) : (
                          <Typography variant="body1">{matchingFeature?.featureValue?.value || 'N/A'}</Typography>
                        )}
                      </ListItemIcon>
                    </ListItem>
                  );
                })}
              </List>
            </MainCard>
          </Grid>

          <Grid item xs={12} md={6} lg={5}>
            <MainCard>
              <RadioGroup value={selectedPlan} onChange={(e) => handlePlanChange(e.target.value)}>
                <Stack spacing={2}>
                  {plans.map((plan) => {
                    const subscribedPlan = subscriptions?.find((s) => s.planId === plan.id);

                    const isSelected = selectedPlan === plan.key;
                    return (
                      <Box
                        key={plan.id}
                        sx={{
                          ...(isSelected ? priceSelectedPlan : priceUnselectedPlan),
                          borderRadius: 2,
                          p: 2
                        }}
                      >
                        <Stack direction="row" justifyContent="space-between" alignItems="flex-start" sx={{ width: '100%' }}>
                          <Stack direction="row" alignItems="center" spacing={1}>
                            <Radio
                              value={plan.key}
                              checked={selectedPlan === plan.key}
                              onChange={() => handlePlanChange(plan.key)}
                              sx={{ p: 0.5 }}
                            />

                            <Typography variant="h5" fontWeight={600}>
                              {plan.name}
                            </Typography>
                            {subscribedPlan ? (
                              <Chip
                                label={subscribedPlan.status}
                                size="small"
                                color={subscribedPlan.status === 'ACTIVE' ? 'success' : 'error'}
                              />
                            ) : (
                              ''
                            )}
                          </Stack>
                        </Stack>
                      </Box>
                    );
                  })}
                </Stack>
              </RadioGroup>
              <Dialog open={showConfirmModal} onClose={() => setShowConfirmModal(false)}>
                <DialogTitle sx={{ color: '#00004F' }}>Confirm Plan Downgrade</DialogTitle>
                <DialogContent>
                  <Typography variant="h6" sx={{ color: '#00004F' }}>
                    You are switching to a lower plan. Some premium features will no longer be available after this change. Please confirm
                    to proceed.
                  </Typography>
                </DialogContent>
                <DialogActions>
                  <Button onClick={() => setShowConfirmModal(false)}>Cancel</Button>
                  <Button
                    onClick={() => {
                      if (pendingPlan) {
                        setSelectedPlan(pendingPlan);
                        setPendingPlan(null);
                      }
                      setShowConfirmModal(false);
                    }}
                    color="error"
                    variant="contained"
                  >
                    Confirm
                  </Button>
                </DialogActions>
              </Dialog>
            </MainCard>
          </Grid>
        </Grid>

        <Grid item xs={12}>
          <Stack direction="row" justifyContent="flex-end" alignItems="center" spacing={2}>
            <Button
              type="submit"
              variant="contained"
              onClick={handleSubscription}
              disabled={subscriptions?.some((subscription) => subscription.planId === currentPlan?.id && subscription.status === 'ACTIVE')}
            >
              continue
            </Button>
          </Stack>
        </Grid>
      </Grid>
    </Grid>
  );
}
