import { useFormik } from 'formik';
import { ChangeEvent, useEffect, useMemo, useState } from 'react';
import { <PERSON>rid, Stack, Button, TextField, InputLabel, Typography, Autocomplete, OutlinedInput, Chip, FormHelperText } from '@mui/material';
import MainCard from 'components/MainCard';
import OutlinedFileInput from 'components/forms/OutlinedFileUpload';
import { useGetUserQuery } from '../../../../redux/auth/authApiSlice';
import { openSnackbar } from 'api/snackbar';
import { SnackbarProps } from 'types/snackbar';
import { DocumentDownload } from 'iconsax-react';
import { FirmDocument } from 'types/auth';
import { useApiErrorHandler } from 'hooks/useApiErrorHandler';
import Loading from 'app/loading';
import { FirmDetailsAction } from './FirmDetailsAction ';
import { useGetFirmDetailsSellerByIdQuery, useUpdateFirmDetailsIdMutation } from 'redux/app/onboard/firmApiSlice';
import { FirmDetailsType } from 'redux/app/types';
import { ALLOWED_FIRM_FILE_EXTENTIONS, FIRM_TYPE_FIELDS } from 'config';
import { updateFirmValidationSchema } from 'validation/firm.validation';
import { useRouter } from 'next/navigation';

export default function FirmDetails() {
  const { data: user } = useGetUserQuery();

  const [, setFirmType] = useState('');
  const options = Object.keys(FIRM_TYPE_FIELDS);
  const [files, setFiles] = useState<{ [key: string]: File }>({});
  const [, setFileKeys] = useState<{ [key: string]: string }>({});
  const [updateFirmDetails, { isLoading: isUpdating, error: updateError, reset: updateReset }] = useUpdateFirmDetailsIdMutation();
  const handleError = useApiErrorHandler();
  const router = useRouter();

  const {
    data: firmDetails,
    refetch: refetchSellerStore,
    isLoading: storeLoading,
    isUninitialized: storeUninitialized
  } = useGetFirmDetailsSellerByIdQuery(user?.profileId ?? '', {
    skip: !user?.profileId
  });

  const handleFirmUpdate = async (values: Partial<FirmDetailsType>) => {
    await updateFirmDetails({
      firmDetailsId: firmDetails?.id ?? '',
      body: {
        typeOfFirm: values.typeOfFirm,
        gstNumber: values.gstNumber,
        panNumber: values.panNumber,
        gstName: values.gstName,
        enrollmentNumber: values.enrollmentNumber,
        firmDocuments: files
      }
    }).unwrap();
    refetchSellerStore();
    openSnackbar({
      open: true,
      message: `Firm details updated successfully!`,
      variant: 'alert',
      alert: { color: 'success' }
    } as SnackbarProps);
    router.push('/account/profile');
  };

  const firmDocumentsMap = useMemo(() => {
    return (firmDetails?.firmDocumentsRelation ?? []).reduce<Record<string, string>>((acc, doc) => {
      if (doc.documentName && doc.documentValue) {
        acc[doc.documentName] = doc.documentValue;
      }
      return acc;
    }, {});
  }, [firmDetails]);

  const formik = useFormik<Partial<FirmDetailsType>>({
    initialValues: {
      typeOfFirm: firmDetails?.typeOfFirm ?? '',
      gstNumber: firmDetails?.gstNumber ?? '',
      gstName: firmDetails?.gstName ?? '',
      panNumber: firmDetails?.panNumber ?? '',
      enrollmentNumber: firmDetails?.enrollmentNumber ?? '',
      files: firmDocumentsMap
    },

    validationSchema: updateFirmValidationSchema,
    onSubmit: async (values) => {
      handleFirmUpdate(values);
    }
  });
  useEffect(() => {
    if (updateError) {
      handleError(updateError);
      updateReset();
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [handleError, updateError]);

  useEffect(() => {
    if (firmDetails && !formik.values?.typeOfFirm) {
      formik.setValues({
        typeOfFirm: firmDetails.typeOfFirm ?? '',
        gstNumber: firmDetails.gstNumber ?? '',
        gstName: firmDetails.gstName ?? '',
        panNumber: firmDetails.panNumber ?? '',
        enrollmentNumber: firmDetails.enrollmentNumber ?? ''
      });
    }
  }, [firmDetails, formik]);

  const handleFileChange = (label: string) => (event: ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];

    if (file) {
      setFiles((prev) => ({
        ...prev,
        [label]: file
      }));

      formik.setFieldValue('files', {
        ...formik.values.files,
        [label]: file.name
      });
    }
  };

  async function handleDownload(doc: FirmDocument) {
    const a = document.createElement('a');
    a.href = doc.documentValue;
    a.target = '_blank';
    a.download = doc.documentName;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  }

  useEffect(() => {
    formik.setFieldTouched('files', true);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [files]);

  useEffect(() => {
    formik.setFieldValue('files', firmDocumentsMap);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [firmDocumentsMap]);

  useEffect(() => {
    if (formik.values.typeOfFirm !== firmDetails?.typeOfFirm) {
      formik.setFieldValue('files', {});
      setFiles({});
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [formik.values.typeOfFirm]);
  if (storeLoading || storeUninitialized) return <Loading />;

  if (!firmDetails && !storeLoading && !storeUninitialized) return <FirmDetailsAction />;

  return (
    <form onSubmit={formik.handleSubmit}>
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <MainCard title="Legal">
            <Grid container spacing={3}>
              {(!formik.values.gstNumber || !formik.values.gstName || formik.values.enrollmentNumber) && (
                <Grid item xs={12} sm={6}>
                  <Stack spacing={1}>
                    <InputLabel htmlFor="enrollmentNumber">Enter Enrollment Number</InputLabel>
                    <OutlinedInput
                      id="enrollmentNumber"
                      name="enrollmentNumber"
                      value={formik.values.enrollmentNumber}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      error={formik.touched.enrollmentNumber && Boolean(formik.errors.enrollmentNumber)}
                      sx={{
                        '& input': {
                          padding: '10px 18px'
                        }
                      }}
                    />
                    {formik.touched.enrollmentNumber && formik.errors.enrollmentNumber && (
                      <Typography variant="caption" color="error">
                        {formik.errors.enrollmentNumber}
                      </Typography>
                    )}
                  </Stack>
                </Grid>
              )}

              <Grid item xs={12} sm={6}>
                <Stack spacing={1}>
                  <InputLabel htmlFor="gstNumber">Enter GST Number</InputLabel>
                  <OutlinedInput
                    id="gstNumber"
                    name="gstNumber"
                    value={formik.values.gstNumber}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={formik.touched.gstNumber && Boolean(formik.errors.gstNumber)}
                    sx={{
                      '& input': {
                        padding: '10px 18px',
                        textTransform: 'uppercase'
                      }
                    }}
                  />
                  {formik.touched.gstNumber && formik.errors.gstNumber && (
                    <Typography variant="caption" color="error">
                      {formik.errors.gstNumber}
                    </Typography>
                  )}
                </Stack>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Stack spacing={1}>
                  <InputLabel htmlFor="gstName">Enter GST Name</InputLabel>
                  <OutlinedInput
                    id="gstName"
                    name="gstName"
                    value={formik.values.gstName}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={formik.touched.gstName && Boolean(formik.errors.gstName)}
                    sx={{}}
                  />
                  {formik.touched.gstName && formik.errors.gstName && (
                    <Typography variant="caption" color="error">
                      {formik.errors.gstName}
                    </Typography>
                  )}
                </Stack>
              </Grid>

              <Grid item xs={12} sm={6}>
                <Stack spacing={1}>
                  <InputLabel htmlFor="panNumber">Enter PAN Number</InputLabel>
                  <OutlinedInput
                    id="panNumber"
                    name="panNumber"
                    value={formik.values.panNumber}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={formik.touched.panNumber && Boolean(formik.errors.panNumber)}
                    sx={{
                      '& input': {
                        padding: '10px 18px',
                        textTransform: 'uppercase'
                      }
                    }}
                  />
                  {formik.touched.panNumber && formik.errors.panNumber && (
                    <Typography variant="caption" color="error">
                      {formik.errors.panNumber}
                    </Typography>
                  )}
                </Stack>
              </Grid>

              <Grid item xs={12} sm={6}>
                <Stack spacing={1}>
                  <InputLabel htmlFor="typeOfFirm">Type of Firm</InputLabel>
                  <Autocomplete
                    options={options}
                    value={formik.values.typeOfFirm}
                    disableClearable
                    onChange={(_, newValue) => {
                      setFirmType(newValue || '');
                      formik.setFieldValue('typeOfFirm', newValue);

                      setFiles({});
                      setFileKeys({});
                    }}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        variant="outlined"
                        name="typeOfFirm"
                        error={formik.touched.typeOfFirm && Boolean(formik.errors.typeOfFirm)}
                        helperText={formik.touched.typeOfFirm && formik.errors.typeOfFirm}
                      />
                    )}
                  />
                </Stack>
              </Grid>

              {formik.values.typeOfFirm &&
                FIRM_TYPE_FIELDS[formik.values.typeOfFirm] &&
                FIRM_TYPE_FIELDS[formik.values.typeOfFirm].map((label, index) => (
                  <Grid item xs={12} sm={6} key={`${formik.values.typeOfFirm}-${index}`}>
                    <Stack spacing={1}>
                      <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
                        {label}
                      </Typography>
                      <OutlinedFileInput
                        name="files"
                        key={`${formik.values.typeOfFirm}-${label}`}
                        id={`file-${formik.values.typeOfFirm}-${index}`}
                        label="Upload files"
                        onChange={handleFileChange(label)}
                        selectedFileName={files[label]?.name ?? ''}
                        accept={ALLOWED_FIRM_FILE_EXTENTIONS.join(', ')}
                      />
                    </Stack>
                  </Grid>
                ))}
              <Grid item xs={12}>
                {formik.errors.files && formik.touched.files && formik.values.typeOfFirm && FIRM_TYPE_FIELDS[formik.values.typeOfFirm] && (
                  <FormHelperText error>{formik.errors.files as string}</FormHelperText>
                )}
              </Grid>

              <Grid item xs={12}>
                <Stack display={'flex'} direction={'row'} flexWrap={'wrap'} spacing={3} rowGap={3}>
                  {firmDetails?.firmDocumentsRelation?.map((doc) => (
                    <Chip
                      key={doc.id}
                      label={doc.documentName}
                      onDelete={() => {
                        handleDownload(doc);
                      }}
                      deleteIcon={<DocumentDownload />}
                    />
                  ))}
                </Stack>
              </Grid>
            </Grid>
          </MainCard>
        </Grid>
        <Grid item xs={12}>
          <Stack direction="row" justifyContent="flex-end" alignItems="center" spacing={2}>
            <Button type="submit" variant="contained" disabled={isUpdating}>
              Update Profile
            </Button>
          </Stack>
        </Grid>
      </Grid>
    </form>
  );
}
