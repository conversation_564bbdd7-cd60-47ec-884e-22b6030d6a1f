'use client';

import { <PERSON>, Typography, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Grid, IconButton } from '@mui/material';
import { TickCircle, CloseCircle, ArrowDown2, ArrowUp2, Information } from 'iconsax-react';
import React, { useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import { useGetGuestTokenMutation } from 'redux/app/terms-and-condition/termsApiSlice';
import { setGuestToken } from 'redux/auth/authSlice';
import { useGetFeaturesWithTokenQuery, useGetPlansWithTokenQuery } from '../../redux/ecom/guest-tokenApiSlice';
import { Feature } from 'types/auth';
import { Tooltip } from '@mui/material';

export default function PricingFeatureTable() {
  const [guestCode, setGuestCode] = useState<string | null>(null);
  const { data: feature = [] } = useGetFeaturesWithTokenQuery(guestCode!, { skip: !guestCode });
  const { data: plans = [] } = useGetPlansWithTokenQuery(guestCode!, {
    skip: !guestCode
  });
  const [expandedCategory, setExpandedCategory] = useState<string[]>([]);

  const [getToken] = useGetGuestTokenMutation();
  const dispatch = useDispatch();
  useEffect(() => {
    const handleLogin = async () => {
      const result = await getToken().unwrap();
      if (result?.accessToken) {
        dispatch(setGuestToken(result));
        setGuestCode(result.accessToken);
      }
    };
    handleLogin();
  }, [getToken, dispatch]);
  const groupedFeatures = feature.reduce((acc: Record<string, Feature[]>, item) => {
    if (!acc[item.category]) acc[item.category] = [];
    acc[item.category].push(item);
    return acc;
  }, {});

  const toggleCategory = (category: string) => {
    setExpandedCategory((prev) => (prev.includes(category) ? prev.filter((c) => c !== category) : [...prev, category]));
  };
  return (
    <Box
      sx={{
        maxWidth: { xs: '100%', sm: 1100, md: 1200 },
        mx: 'auto',
        borderRadius: '16px',
        overflow: 'hidden',
        backgroundColor: '#EFF1F2'
      }}
    >
      <Grid item xs={12}>
        <Typography
          variant="h4"
          fontWeight="bold"
          color="#000050"
          gutterBottom
          sx={{
            textAlign: 'left',
            fontWeight: 'bold',
            fontSize: { xs: '1rem', sm: '1.2rem', md: '1.995rem' },
            // p: { xs: 2, sm: 5, md: 5 },
            pl: { xs: 2, sm: 4, md: 10 },
            pt: { xs: 2, sm: 5, md: 5 }
          }}
        >
          Full Feature List
        </Typography>
      </Grid>

      <Box>
        <Box
          sx={{
            mx: 'auto',
            maxWidth: 1100,

            p: { xs: 2, sm: 5 },
            backgroundColor: '#EFF1F2'
          }}
        >
          <TableContainer
            component={Paper}
            sx={{ borderRadius: '16px', overflow: 'hidden', boxShadow: 'none', border: '1px solid #000050' }}
          >
            <Table sx={{ minWidth: 650 }}>
              <TableHead sx={{ borderBottom: '1px solid #000050' }}>
                <TableRow>
                  <TableCell
                    sx={{
                      fontWeight: 'bold',
                      fontSize: '20px',
                      color: '#000050',
                      textTransform: 'capitalize'
                    }}
                  >
                    Feature
                  </TableCell>
                  {plans
                    .slice()
                    .reverse()
                    .map((plan, index) => (
                      <TableCell
                        key={index}
                        sx={{
                          fontWeight: 'bold',
                          fontSize: '18px',
                          textAlign: 'center',
                          color: '#000050',
                          borderLeft: '1px solid #000050',
                          textTransform: 'capitalize'
                        }}
                      >
                        {plan.name}
                      </TableCell>
                    ))}
                </TableRow>
              </TableHead>
              <TableBody>
                {Object.entries(groupedFeatures).map(([category, featuresInCategory]) => (
                  <React.Fragment key={category}>
                    <TableRow onClick={() => toggleCategory(category)} sx={{ cursor: 'pointer' }}>
                      <TableCell
                        colSpan={plans.length + 1}
                        sx={{
                          backgroundColor: '#fff',
                          fontWeight: 'bold',
                          fontSize: '16px',
                          color: '#000050',
                          borderTop: '1px solid #000050',
                          textTransform: 'uppercase',
                          py: 1.5
                        }}
                      >
                        <Box
                          sx={{
                            display: 'flex',
                            justifyContent: 'space-between',
                            alignItems: 'center'
                          }}
                        >
                          {category}
                          {expandedCategory.includes(category) ? (
                            <ArrowUp2 size="20" color="#000050" />
                          ) : (
                            <ArrowDown2 size="20" color="#000050" />
                          )}
                        </Box>
                      </TableCell>
                    </TableRow>

                    {expandedCategory.includes(category) &&
                      featuresInCategory.map((featureItem, idx) => (
                        <TableRow key={idx}>
                          <TableCell
                            sx={{
                              textAlign: 'left',
                              borderLeft: 'none'
                            }}
                          >
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              <Typography fontSize="1rem">{featureItem.name}</Typography>
                              {featureItem.description && (
                                <Tooltip title={featureItem.description} arrow>
                                  <IconButton size="small" sx={{ p: 0 }}>
                                    <Information size="16" variant="Outline" color="#000050" />
                                  </IconButton>
                                </Tooltip>
                              )}
                            </Box>
                          </TableCell>

                          {[...plans].reverse().map((plan, planIdx) => {
                            const featureMatch = plan.planFeatureValues.find((val) => val.featureValue?.feature?.id === featureItem.id);
                            const val = featureMatch?.featureValue?.value;

                            return (
                              <TableCell
                                key={planIdx}
                                sx={{
                                  textAlign: 'center'
                                }}
                              >
                                {val === 'true' ? (
                                  <TickCircle size="22" color="#4CAF50" variant="Bold" />
                                ) : val === 'false' ? (
                                  <CloseCircle size="22" color="#F44336" variant="Bold" />
                                ) : (
                                  <Typography fontSize="14px">{val || 'N/A'}</Typography>
                                )}
                              </TableCell>
                            );
                          })}
                        </TableRow>
                      ))}
                  </React.Fragment>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </Box>
      </Box>
    </Box>
  );
}
