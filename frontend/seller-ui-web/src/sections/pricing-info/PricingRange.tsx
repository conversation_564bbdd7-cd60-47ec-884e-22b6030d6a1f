'use client';

import { Container, Grid } from '@mui/material';
import { Box, Typography, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Card, CardContent } from '@mui/material';

const fixedFeeData = [
  { priceRange: '0 - 300', rate: '₹ 000' },
  { priceRange: '300 - 500', rate: '₹ 000' },
  { priceRange: '500 - 1000', rate: '₹ 000' },
  { priceRange: 'More than 1000', rate: '₹ 000' }
];

export default function FixedFeeSection() {
  return (
    <Container maxWidth="lg">
      <Grid container spacing={4} mt={1}>
        <Grid item xs={12}>
          {' '}
          <Card
            sx={{
              backgroundColor: 'white',
              borderRadius: 3,
              boxShadow: 'none',
              width: '100%'
            }}
          >
            <CardContent sx={{ pl: 10 }}>
              <Grid item xs={12}>
                <Typography
                  variant="h4"
                  sx={{
                    color: '#000050',
                    fontWeight: 'bold',
                    mb: 3,
                    textAlign: 'left',
                    fontSize: { xs: '1rem', sm: '1.2rem', md: '1.995rem' }
                  }}
                >
                  Fixed fee{' '}
                </Typography>
              </Grid>

              <Grid item xs={12} sx={{ textAlign: 'left', mb: 4 }}>
                <Typography
                  variant="body1"
                  sx={{
                    color: '#555',
                    fontSize: { xs: '0.95rem', md: '1rem' },
                    lineHeight: 1.6,
                    maxWidth: { md: '70%' },
                    mb: 4
                  }}
                >
                  A small fixed fee is charged for all orders that you complete successfully. The Fixed fee will vary depending on the price
                  of product.
                </Typography>
              </Grid>
              <Grid item xs={12}>
                {' '}
                <Box
                  sx={{
                    display: 'flex',
                    justifyContent: 'flex-start'
                  }}
                >
                  <TableContainer
                    sx={{
                      maxWidth: 500,
                      border: '1px solid #d0d0d0',
                      borderRadius: 3,
                      overflow: 'hidden',
                      width: { xs: '100%', sm: '80%', md: '60%' }
                    }}
                  >
                    <Table>
                      <TableHead>
                        <TableRow
                          sx={{
                            backgroundColor: '#EFF1F2'
                          }}
                        >
                          <TableCell
                            align="center"
                            sx={{
                              fontWeight: 'bold',
                              fontSize: '0.85rem',
                              bgcolor: '#b1b8c6',
                              borderRight: '1px solid #b0b0b0'
                            }}
                          >
                            Item Price Range
                          </TableCell>
                          <TableCell
                            align="center"
                            sx={{
                              fontWeight: 'bold',
                              color: '#0a0a75',
                              fontSize: '0.85rem'
                            }}
                          >
                            Rate
                          </TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {fixedFeeData.map((row, index) => (
                          <TableRow key={index}>
                            <TableCell
                              align="left"
                              sx={{
                                fontSize: '1rem',
                                color: '#000',
                                borderRight: '1px solid #e0e0e0',
                                borderBottom: '1px solid #e0e0e0'
                              }}
                            >
                              {row.priceRange}
                            </TableCell>
                            <TableCell
                              align="left"
                              sx={{
                                fontSize: '1rem',
                                color: '#000',
                                borderBottom: '1px solid #e0e0e0'
                              }}
                            >
                              {row.rate}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </Box>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Container>
  );
}
