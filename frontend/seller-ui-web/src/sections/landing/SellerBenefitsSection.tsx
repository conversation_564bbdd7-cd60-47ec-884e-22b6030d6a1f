'use client';
import React, { useState } from 'react';
import { Container, Typography, Card, CardContent, Accordion, AccordionSummary, AccordionDetails, Grid } from '@mui/material';
import { ArrowDown2 } from 'iconsax-react';
import Image from 'next/image';

const flexibilityOptions = [
  {
    question: 'Receive orders according to your available time',
    answer:
      'Showcase your creativity and passion to the world and receive orders only according to your available time by setting up your Store Settings, whether you are a student, employee,  parent or a small business'
  },
  {
    question: 'Open and close your store manually',
    answer:
      'Looking to take some time off for a family event, exam preparation, or a busy week ahead? No problem! You can easily and manually open or close your store. Simply close your store whenever you need a break, and you wont receive any new orders until you are ready to reopen.'
  },
  {
    question: 'Option to Turn On & Off Custom Orders',
    answer:
      'Want to offer custom products tailored to your customers unique needs? Turn on Custom Orders and communicate directly with your customers to take care of their custom requirements. But if you are short on time and cant manage the Custom Orders and additional communications, turn off Custom Orders.'
  },
  {
    question: 'Choose Between Self-Ship or EcomDukes Shipping ',
    answer:
      'Take control of your shipping options with our system. Choose between our affordable EcomDukes Shipping or the Self-Ship option that might suit your specific business needs. Either way, we have got you covered for a hassle-free experience.'
  }
];

const sellerBenefitsOptions = [
  {
    question: 'GST Registration to sell all over India.',
    answer: 'Not a GST Registered Seller Yet? We offer GST Registration Service to help you sell all over India.'
  },
  {
    question: 'Solid support to troubleshoot issues',
    answer:
      'Our support team is available to address any questions or concerns, whether it is technical assistance or general inquiries, we are here to provide the support you need, when you need it.'
  },
  {
    question: 'Marketing support to reach more customers',
    answer:
      'We create marketing campaigns across multiple channels, search engine optimization, email marketing, and online advertisements to increase visibility and drive traffic to our platform'
  },
  {
    question: 'Free promotions via social media channels',
    answer: 'Free promotions to reach more people via our Social Media Channels if you are eligible'
  }
];

function SellerBenefitsPage() {
  const [expandedId, setExpandedId] = useState<string | null>(null);

  const handleAccordionToggle = (id: string) => (_event: React.SyntheticEvent, isExpanded: boolean) => {
    setExpandedId(isExpanded ? id : null);
  };
  const [benefitsExpandedId, setBenefitsExpandedId] = useState<string | null>(null);
  return (
    <Container
      maxWidth="lg"
      sx={{
        mt: { xs: 2, sm: 2 },
        bgcolor: '#f9f9f9',
        p: { xs: 2, sm: 3 },
        borderRadius: 2,
        gap: 'none'
      }}
    >
      <Card
        sx={{
          backgroundColor: 'white',
          borderRadius: 3,
          // p: { xs: 2, sm: 3 },
          boxShadow: 'none',
          width: '100%'
        }}
      >
        <CardContent sx={{ px: { xs: 2, sm: 4, md: 8, lg: 10 }, py: { xs: 2, sm: 2 } }}>
          <Grid container>
            {' '}
            <Typography
              variant="h6"
              sx={{
                fontWeight: 'bold',
                fontSize: { xs: '1rem', sm: '1.2rem', md: '1.995rem' },

                color: '#00004F'
              }}
            >
              Benefits of selling on EcomDukes{' '}
            </Typography>
          </Grid>

          <Grid container spacing={4}>
            <Grid item xs={12} md={6}>
              <Typography
                variant="h6"
                sx={{
                  fontWeight: 'bold',
                  fontSize: { xs: '1rem', sm: '1.2rem', md: '1.15rem' },
                  color: '#4A4A4A',
                  mb: 2,
                  mt: 5
                }}
              >
                Flexibility and Time Management{' '}
              </Typography>
              {flexibilityOptions.map((item, index) => {
                const id = `flex-${index}`;
                return (
                  <Accordion key={id} expanded={expandedId === id} onChange={handleAccordionToggle(id)}>
                    <AccordionSummary
                      expandIcon={<ArrowDown2 />}
                      sx={{
                        justifyContent: 'space-between',
                        textTransform: 'none',
                        color: '#4A4A4A',
                        border: '1px solid #D8D8D8',
                        borderRadius: '12px',
                        fontSize: '16px',
                        fontWeight: 400,
                        padding: '12px 16px',
                        backgroundColor: 'white'
                      }}
                    >
                      {item.question}
                    </AccordionSummary>
                    <AccordionDetails sx={{ bgcolor: '#f1f1f1', borderRadius: '12px' }}>{item.answer}</AccordionDetails>
                  </Accordion>
                );
              })}
            </Grid>

            <Grid item xs={12} md={6} sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
              <Image src="/assets/images/landing/business-management.avif" alt="Support Illustration" width={500} height={400} />
            </Grid>
          </Grid>
          <Grid container spacing={4}>
            <Grid item xs={12} md={6} sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
              <Image src="/assets/images/landing/growth.avif" alt="Support Illustration" width={500} height={400} />
            </Grid>

            <Grid item xs={12} md={6}>
              <Typography
                variant="h6"
                sx={{
                  fontWeight: 'bold',
                  fontSize: { xs: '1rem', sm: '1.2rem', md: '1.15rem' },
                  color: '#4A4A4A',
                  mb: 2,
                  mt: 5
                }}
              >
                Support and Growth{' '}
              </Typography>
              {sellerBenefitsOptions.map((benefit, index) => {
                const id = `benefit-${index}`;
                return (
                  <Accordion
                    key={id}
                    expanded={benefitsExpandedId === id}
                    onChange={(_, isExpanded) => setBenefitsExpandedId(isExpanded ? id : null)}
                    sx={{ borderRadius: '20px', mt: index !== 0 ? 2 : 0 }}
                  >
                    <AccordionSummary
                      expandIcon={<ArrowDown2 />}
                      sx={{
                        justifyContent: 'space-between',
                        textTransform: 'none',
                        color: '#4A4A4A',
                        border: '1px solid #D8D8D8',
                        borderRadius: '12px',
                        fontSize: '16px',
                        fontWeight: 400,
                        padding: '12px 16px',
                        backgroundColor: 'white'
                      }}
                    >
                      {benefit.question}
                    </AccordionSummary>
                    <AccordionDetails sx={{ bgcolor: '#f1f1f1', borderRadius: '12px' }}>{benefit.answer}</AccordionDetails>
                  </Accordion>
                );
              })}
            </Grid>
          </Grid>
          <Grid container spacing={6} alignItems="center">
            <Grid item xs={12} md={6}>
              <Typography
                variant="h6"
                sx={{
                  fontWeight: 'bold',
                  fontSize: { xs: '1rem', sm: '1.2rem', md: '1.15rem' },
                  color: '#4A4A4A',
                  mb: 2,
                  mt: 5,
                  width: { xs: '100%', sm: '70%', md: '60%' }
                }}
              >
                Multiple Product Personalisation Options and Product Variants
              </Typography>
              <Typography
                sx={{ color: '#4A4A4A', fontSize: { xs: '0.9rem', md: '1rem' }, mb: 2, width: { xs: '100%', sm: '80%', md: '70%' } }}
              >
                Let your customers personalize the product they want in their unique way by offering multiple Personalisation and Variant
                options.
              </Typography>
              <Typography sx={{ color: '#4A4A4A', fontSize: { xs: '0.9rem', md: '1rem', width: { xs: '100%', sm: '80%', md: '70%' } } }}>
                Set different prices for different variants with peculiar qualities.
              </Typography>
            </Grid>

            <Grid item xs={12} md={6} sx={{ textAlign: 'center' }}>
              <Image
                src="/assets/images/landing/person.avif"
                alt="Personalization Illustration"
                width={500}
                height={400}
                style={{ maxWidth: '100%', height: 'auto' }}
              />
            </Grid>

            <Grid item xs={12} md={6} sx={{ textAlign: 'center' }}>
              <Image
                src="/assets/images/landing/appoinment.webp"
                alt="Order Management Illustration"
                width={500}
                height={400}
                style={{ maxWidth: '100%', height: 'auto' }}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <Typography
                variant="h6"
                sx={{
                  fontWeight: 'bold',
                  fontSize: { xs: '1rem', sm: '1.2rem', md: '1.15rem' },
                  color: '#4A4A4A',
                  mb: 2,
                  mt: 5
                }}
              >
                Order Management and Order Tracking
              </Typography>
              <Typography sx={{ color: '#4A4A4A', fontSize: { xs: '0.9rem', md: '1rem' }, mb: 2, width: '60%' }}>
                Easily track incoming orders, update order statuses, and manage inventory, ensuring smooth and timely fulfillment of
                customer requests.
              </Typography>
              <Typography sx={{ color: '#4A4A4A', fontSize: { xs: '0.9rem', md: '1rem', width: '60%' } }}>
                Our integrated order tracking system allows you and your customers to easily monitor the progress of each shipment.
              </Typography>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    </Container>
  );
}

export default SellerBenefitsPage;
