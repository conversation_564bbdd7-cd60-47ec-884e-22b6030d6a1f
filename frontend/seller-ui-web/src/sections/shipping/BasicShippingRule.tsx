import { Grid, InputLabel, TextField } from '@mui/material';
import { openSnackbar } from 'api/snackbar';
import LoadingButton from 'components/@extended/LoadingButton';
import { useFormik } from 'formik';
import { useMemo } from 'react';
import { useGetFirmDetailsSellerByIdQuery } from 'redux/app/onboard/firmApiSlice';
import { useGetSellerStoreBySellerIdQuery } from 'redux/auth/sellerApiSlice';
import { useUpdateShippingProfileMutation } from 'redux/ecom/shippingApiSlice';
import { BasicShippingCharge, BasicShippingStateType } from 'types/shipping';
import { SnackbarProps } from 'types/snackbar';
import * as Yup from 'yup';

interface Props {
  basicShippingCharges?: BasicShippingCharge[];
  profileId: string;
  sellerId: string;
}

export default function BasicShippingRule({ basicShippingCharges, profileId, sellerId }: Props) {
  const { data: selelrStore } = useGetSellerStoreBySellerIdQuery(sellerId, {
    skip: !sellerId
  });
  const { data: selelrFirm } = useGetFirmDetailsSellerByIdQuery(sellerId, {
    skip: !sellerId
  });

  const [updateShippingProfile, { isLoading: isUpdating }] = useUpdateShippingProfileMutation();

  const formik = useFormik({
    initialValues: {
      inStatePrice: basicShippingCharges?.find((charge) => charge.stateType === BasicShippingStateType.IN_STATE)?.price || 0,
      outStatePrice: basicShippingCharges?.find((charge) => charge.stateType === BasicShippingStateType.OUT_STATE)?.price || 0
    },
    validationSchema: Yup.object().shape({
      inStatePrice: Yup.number().required('In-state price is required').min(0, 'Must be >= 0'),
      outStatePrice: Yup.number().required('Out-of-state price is required').min(0, 'Must be >= 0')
    }),
    onSubmit: handleSubmit
  });

  const sellerState = useMemo(() => selelrStore?.state, [selelrStore]);
  const hasPAN = useMemo(() => !!selelrFirm?.panNumber, [selelrFirm]);

  async function handleSubmit(values: { inStatePrice: number; outStatePrice: number }) {
    const basicShippingCharges = [
      {
        shippingProfileId: profileId,
        stateType: BasicShippingStateType.IN_STATE,
        price: values.inStatePrice,
        isActive: true
      },
      {
        shippingProfileId: profileId,
        stateType: BasicShippingStateType.OUT_STATE,
        price: values.outStatePrice,
        isActive: true
      }
    ];

    await updateShippingProfile({
      id: profileId,
      body: { basicShippingCharges }
    }).unwrap();

    openSnackbar({
      open: true,
      message: 'Basic shipping charges updated successfully',
      variant: 'alert',
      alert: { color: 'success' }
    } as SnackbarProps);
  }

  return (
    <form onSubmit={formik.handleSubmit}>
      <Grid container spacing={2}>
        <Grid item xs={12} sm={6}>
          <InputLabel htmlFor="inStatePrice"> {`In-State Price (₹) ${sellerState ? `(${sellerState})` : ''}`}</InputLabel>
          <TextField
            fullWidth
            name="inStatePrice"
            type="number"
            value={formik.values.inStatePrice}
            onChange={formik.handleChange}
            error={formik.touched.inStatePrice && Boolean(formik.errors.inStatePrice)}
            helperText={formik.touched.inStatePrice && formik.errors.inStatePrice}
          />
        </Grid>

        <Grid item xs={12} sm={6}>
          <InputLabel htmlFor="inStatePrice"> Out-of-State Price (₹)</InputLabel>
          <TextField
            fullWidth
            name="outStatePrice"
            type="number"
            disabled={!hasPAN}
            value={formik.values.outStatePrice}
            onChange={formik.handleChange}
            error={formik.touched.outStatePrice && Boolean(formik.errors.outStatePrice)}
            helperText={
              !hasPAN ? 'Provide PAN to enable out-of-state pricing' : formik.touched.outStatePrice && formik.errors.outStatePrice
            }
          />
        </Grid>

        <Grid item xs={12}>
          <LoadingButton type="submit" variant="contained" loading={isUpdating} sx={{ mt: 2 }}>
            Submit
          </LoadingButton>
        </Grid>
      </Grid>
    </form>
  );
}
