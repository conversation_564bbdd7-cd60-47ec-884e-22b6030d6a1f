import { ReactNode } from 'react';

// material-ui
import Box from '@mui/material/Box';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import Skeleton from '@mui/material/Skeleton';
import { useTheme } from '@mui/material/styles';

// project-imports
import MainCard from 'components/MainCard';

// ==============================|| ANALYTICS CARD ||============================== //

interface AnalyticsCardProps {
  title: string;
  count: number;
  isLoading?: boolean;
  color?: 'primary' | 'secondary' | 'success' | 'error' | 'warning' | 'info';
  icon?: ReactNode;
  subtitle?: string;
}

export default function AnalyticsCard({ title, count, isLoading = false, color = 'primary', icon, subtitle }: AnalyticsCardProps) {
  const theme = useTheme();

  const getColorValue = (colorName: string) => {
    switch (colorName) {
      case 'primary':
        return theme.palette.primary.main;
      case 'secondary':
        return theme.palette.secondary.main;
      case 'success':
        return theme.palette.success.main;
      case 'error':
        return theme.palette.error.main;
      case 'warning':
        return theme.palette.warning.main;
      case 'info':
        return theme.palette.info.main;
      default:
        return theme.palette.primary.main;
    }
  };

  return (
    <MainCard content={false}>
      <Box sx={{ p: 2.25 }}>
        <Stack spacing={0.5}>
          <Stack direction="row" alignItems="center" justifyContent="space-between">
            <Typography variant="h6" color="textSecondary">
              {title}
            </Typography>
            {icon && (
              <Box
                sx={{
                  color: getColorValue(color),
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}
              >
                {icon}
              </Box>
            )}
          </Stack>

          {isLoading ? (
            <Skeleton variant="text" width="60%" height={40} />
          ) : (
            <Typography variant="h4" color="inherit">
              {count.toLocaleString()}
            </Typography>
          )}

          {subtitle && (
            <Typography variant="caption" color="textSecondary">
              {subtitle}
            </Typography>
          )}
        </Stack>
      </Box>
    </MainCard>
  );
}
