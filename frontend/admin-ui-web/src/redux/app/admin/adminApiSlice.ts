import { SubAdmin } from 'types/admin';
import { ApiSliceIdentifier } from '../../../enums/api.enum';
import { apiSlice } from '../../../redux/apiSlice';
import { Users } from 'types/user';
import { ApiPermission } from 'types/permission';
import { Count } from 'types/api';

export const adminApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getAdmins: builder.query<
      SubAdmin[],
      {
        limit: number;
        skip?: number;
        order?: Array<Record<string, unknown> | string>;
        where?: Record<string, unknown>;
        fields?: Record<string, boolean>;
        include?: Array<Record<string, unknown> | string>;
      }
    >({
      query: ({ limit, skip, order, where, fields, include }) => ({
        url: '/admins',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: JSON.stringify({
            limit,
            offset: skip,
            order,
            where,
            fields,
            include
          })
        }
      })
    }),
    getAdminsCount: builder.query<
      Count,
      {
        where?: Record<string, unknown>;
        include?: Array<Record<string, unknown> | string>;
      }
    >({
      query: ({ where, include }) => ({
        url: '/admins/count',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: JSON.stringify({
            where,
            include
          })
        }
      })
    }),
    createAdmin: builder.mutation<void, FormData>({
      query: (newAdmin) => ({
        url: '/admins',
        method: 'POST',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        body: newAdmin
      })
    }),
    updateAdmin: builder.mutation<void, { id: string; data: FormData }>({
      query: ({ id, data }) => ({
        url: `/admins/${id}`,
        method: 'PATCH',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        body: data
      })
    }),
    getAdminsById: builder.query<SubAdmin, { id: string; include?: Array<Record<string, unknown> | string> }>({
      query: ({ id, include }) => ({
        url: `/admins/${id}`,
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: JSON.stringify({
            include: include || []
          })
        }
      })
    }),
    getPermissions: builder.query<ApiPermission[], void>({
      query: () => ({
        url: '/permissions',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE
      })
    }),
    removeAdmin: builder.mutation<void, string>({
      query: (id) => ({
        url: `/admins/${id}`,
        method: 'DELETE',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE
      })
    }),
    updateAdminStatus: builder.mutation<void, { id: string; status: string }>({
      query: ({ id, status }) => ({
        url: `/admins/${id}/status`,
        method: 'PATCH',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        body: { status }
      })
    }),

    getUsers: builder.query<
      Users[],
      {
        limit?: number;
        skip?: number;
        order?: Array<Record<string, unknown> | string>;
        where?: Record<string, unknown>;
        fields?: Record<string, boolean>;
        include?: Array<Record<string, unknown> | string>;
      }
    >({
      query: ({ limit, skip, order = ['createdOn DESC'], where, include } = {}) => ({
        url: '/sellers',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.AUTH_SERVICE,
        params: {
          filter: JSON.stringify({
            limit,
            offset: skip,
            order,
            where,
            include: [
              {
                relation: 'userTenant',
                scope: {
                  include: [
                    {
                      relation: 'user'
                    }
                  ]
                }
              },
              ...(include || [])
            ]
          })
        }
      })
    })
  })
});

export const {
  useGetAdminsQuery,
  useGetAdminsCountQuery,
  useRemoveAdminMutation,
  useGetAdminsByIdQuery,
  useGetPermissionsQuery,
  useCreateAdminMutation,
  useUpdateAdminMutation,
  useUpdateAdminStatusMutation,
  useGetUsersQuery
} = adminApiSlice;
