import { convertFromRaw } from 'draft-js';

/**
 * Utility function to render rich text content from Draft.js raw format
 * @param rawContent - The raw content string from Draft.js editor
 * @returns Plain text string or the original content if parsing fails
 */
export const renderRichText = (rawContent?: string): string => {
  if (!rawContent) return '';
  
  try {
    const contentState = convertFromRaw(JSON.parse(rawContent));
    return contentState.getPlainText();
  } catch (err) {
    console.warn('Failed to parse rich text content:', err);
    return rawContent;
  }
};

/**
 * Utility function to render rich text content as HTML
 * @param rawContent - The raw content string from Draft.js editor
 * @returns HTML string or the original content if parsing fails
 */
export const renderRichTextAsHTML = (rawContent?: string): string => {
  if (!rawContent) return '';
  
  try {
    const contentState = convertFromRaw(JSON.parse(rawContent));
    // For HTML rendering, you might want to use a library like draft-js-export-html
    // For now, returning plain text with line breaks preserved
    return contentState.getPlainText().replace(/\n/g, '<br />');
  } catch (err) {
    console.warn('Failed to parse rich text content as HTML:', err);
    return rawContent.replace(/\n/g, '<br />');
  }
};
