'use client';

import { Container, CircularProgress, Typography } from '@mui/material';
import { useGetFacetByIdQuery } from 'redux/app/facet/facetApiSlice';
import FacetCreate from 'views/facets/FacetCreate';

const FacetEditPage = ({ params }: { params: { id: string } }) => {
  const { id } = params;

  const {
    data: facetData,
    isLoading,
    error,
    refetch
  } = useGetFacetByIdQuery({
    id,
    include: ['facetValues']
  });

  if (isLoading) {
    return (
      <Container sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <CircularProgress />
      </Container>
    );
  }

  if (error || !facetData) {
    return (
      <Container sx={{ textAlign: 'center', mt: 4 }}>
        <Typography color="error" variant="h6">
          Failed to load Tag
        </Typography>
      </Container>
    );
  }

  const initialValues = {
    name: facetData.name ?? '',
    values: facetData.facetValues?.map((v: any) => ({ name: v.name })) ?? []
  };

  return (
    <Container maxWidth="md">
      <FacetCreate initialValues={initialValues} isEdit facetId={id} refetch={refetch} />
    </Container>
  );
};

export default FacetEditPage;
