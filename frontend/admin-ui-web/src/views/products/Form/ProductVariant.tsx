import { Autocomplete, Button, Chip, FormControl, Grid, InputLabel, MenuItem, Select, TextField } from '@mui/material';
import MainCard from 'components/MainCard';
import { FormikProps } from 'formik';
import { FC, useEffect } from 'react';
import { OptionValue, ProductDto } from 'types/product-dto';
import { VariantFormTable } from './VariantFormTable';
import { generateVariantsWithPreservedValues } from 'utils/generateVariantsWithPreservedValues';
import { VariantUnit } from 'enums/product.enum';
import { capitalizeFirst } from 'utils/capitalizeFirst';
interface ProductVariantFormProps {
  formik: FormikProps<ProductDto>;
}
export const ProductVariantForm: FC<ProductVariantFormProps> = ({ formik }) => {
  const { values } = formik;
  const handleAddOption = () => {
    formik.setFieldValue('options', [
      ...formik.values.options,
      {
        id: formik.values.options.length + 1,
        name: '',
        values: []
      }
    ]);
  };

  useEffect(() => {
    if (formik.values.options.length > 0) {
      const updatedVariants = generateVariantsWithPreservedValues(formik.values.options, formik.values.variants || []);
      formik.setFieldValue('variants', updatedVariants);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [formik.values.options]);

  return (
    <MainCard title="Product Variant" content={false} sx={{ padding: 0, margin: 0 }}>
      <Grid container spacing={2} sx={{ p: 2 }} direction="column">
        {values?.options?.map((option, idx) => (
          <Grid container spacing={1} key={option.id ?? idx} padding={2}>
            <Grid item xs={4}>
              <TextField
                name={`options[${idx}].name`}
                value={option.name}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                fullWidth
                label="Option Name"
                variant="outlined"
                size="small"
                error={!!formik.touched.options?.[idx]?.name && !!(formik.errors.options?.[idx] as any)?.name}
                helperText={formik.touched.options?.[idx]?.name && (formik.errors.options?.[idx] as any)?.name}
                placeholder="Eg Size, Color, etc."
              />
            </Grid>
            <Grid item xs={2}>
              <FormControl fullWidth>
                <InputLabel>Unit</InputLabel>
                <Select
                  name={`options[${idx}].unit`}
                  value={option.unit}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  size="small"
                  error={!!formik.touched.options?.[idx]?.unit && !!(formik.errors.options?.[idx] as any)?.unit}
                >
                  {Object.values(VariantUnit).map((unit) => (
                    <MenuItem key={unit} value={unit}>
                      {capitalizeFirst(unit)}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={6}>
              <Autocomplete
                multiple
                freeSolo
                options={[]}
                value={formik.values.options[idx].values?.map((v: OptionValue) => v.name) || []}
                onChange={(_, newValue) => {
                  formik.setFieldValue(
                    `options[${idx}].values`,
                    newValue.map((val, id) => ({ name: val, id }))
                  );
                }}
                renderTags={(value: readonly string[], getTagProps) =>
                  value.map((option, index) => <Chip variant="outlined" label={option} {...getTagProps({ index })} key={index} />)
                }
                size="small"
                renderInput={(params) => (
                  <TextField
                    {...params}
                    variant="outlined"
                    size="small"
                    label="Option Values"
                    placeholder="Enter and press enter"
                    error={!!formik.touched.options?.[idx]?.values && !!(formik.errors.options?.[idx] as any)?.values}
                    helperText={formik.touched.options?.[idx]?.values && (formik.errors.options?.[idx] as any)?.values}
                  />
                )}
              />
            </Grid>
          </Grid>
        ))}

        <Grid item>
          <Button variant="outlined" onClick={handleAddOption}>
            Add Option
          </Button>
        </Grid>
        {!!formik.values.variants?.length && (
          <Grid item xs={12}>
            <VariantFormTable formik={formik} />
          </Grid>
        )}
      </Grid>
    </MainCard>
  );
};
