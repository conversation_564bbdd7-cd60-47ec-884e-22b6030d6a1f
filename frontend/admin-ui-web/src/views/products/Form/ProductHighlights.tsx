import { Grid } from '@mui/material';
import MainCard from 'components/MainCard';
import { FormikProps } from 'formik';
import { FC } from 'react';
import { ProductDto } from 'types/product-dto';
import { ProductDetailFormTable } from './ProductDetailForm';
import { UniquenessForm } from './ProductUniqueness';
import { SuitabilityForm } from './ProductSuitabilityForm';
import { PersonalizedInfoForm } from './ProductPersonalizedInfoForm';
import { ProductVariantUpdateDto } from 'types/product-variant';
import { MoreInfoForm } from './ProductMoreInfo';

interface Props {
  formik: FormikProps<ProductDto> | FormikProps<ProductVariantUpdateDto>;
}

export const ProductHighlights: FC<Props> = ({ formik }) => {
  return (
    <MainCard title="Product Highlights" content={false} sx={{ padding: 0, margin: 0 }}>
      <Grid container spacing={2} sx={{ p: 2 }} rowGap={2}>
        <Grid item xs={12}>
          <ProductDetailFormTable formik={formik} />
        </Grid>
        <Grid item xs={12}>
          <MoreInfoForm formik={formik} />
        </Grid>
        <Grid item xs={12}>
          <UniquenessForm formik={formik} />
        </Grid>
        <Grid item xs={12}>
          <SuitabilityForm formik={formik} />
        </Grid>
        <Grid item xs={12}>
          <PersonalizedInfoForm formik={formik} />
        </Grid>
      </Grid>
    </MainCard>
  );
};
