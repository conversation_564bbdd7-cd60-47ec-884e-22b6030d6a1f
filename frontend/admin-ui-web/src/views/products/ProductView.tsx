'use client';

import { useState, SyntheticEvent, useMemo } from 'react';

// material-ui
import Tab from '@mui/material/Tab';
import Tabs from '@mui/material/Tabs';
import Grid from '@mui/material/Grid';
import Stack from '@mui/material/Stack';
import Divider from '@mui/material/Divider';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import { renderRichText } from 'utils/richTextUtils';

// project-imports
import MainCard from 'components/MainCard';
import CircularLoader from 'components/CircularLoader';

import { ProductDisclaimer, ProductTabsProps } from 'types/product';
import { useGetproductByIdQuery } from 'redux/app/products/productApiSlice';
import ProductImages from './product-details/ProductImages';
import ProductInfo from './product-details/ProductInfo';
import ProductSpecifications from './product-details/ProductSpecifications';
import { customIncludes } from 'constants/product';
import ProductDetails from './product-details/ProductDetails';
import ProductVariantsList from './product-details/ProductVariantsList';
import ProductCustomizationFieldsTable from './product-details/ProductCustomizationFieldsTable';

function TabPanel({ children, value, index, ...other }: ProductTabsProps) {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`product-details-tabpanel-${index}`}
      aria-labelledby={`product-details-tab-${index}`}
      {...other}
    >
      {value === index && <Box>{children}</Box>}
    </div>
  );
}

function a11yProps(index: number) {
  return {
    id: `product-details-tab-${index}`,
    'aria-controls': `product-details-tabpanel-${index}`
  };
}

type Props = {
  id: string;
};

export default function ProductView({ id }: Props) {
  const {
    data: product,
    isLoading,
    isFetching,
    refetch
  } = useGetproductByIdQuery(
    { id, filter: { include: customIncludes } },
    {
      refetchOnMountOrArgChange: true
    }
  );

  const [value, setValue] = useState(0);

  const handleChange = (_event: SyntheticEvent, newValue: number) => {
    setValue(newValue);
  };

  const productImages = useMemo(() => <ProductImages product={product!} />, [product]);

  const loader = (
    <Box sx={{ height: 504 }}>
      <CircularLoader />
    </Box>
  );

  const isLoader = isLoading || isFetching || product === null;

  return (
    <Grid container spacing={1}>
      <Grid item xs={12}>
        {isLoader ? (
          <MainCard>{loader}</MainCard>
        ) : (
          <Grid container spacing={3}>
            <Grid item xs={12} sm={8} md={5} lg={4}>
              <Stack spacing={2}>
                {productImages}
                <Box
                  sx={{
                    bgcolor: 'secondary.lighter',
                    p: 2,
                    borderRadius: 1,
                    boxShadow: '0 2px 4px rgba(0,0,0,0.05)',
                    height: 200,
                    overflow: 'auto'
                  }}
                >
                  <ProductInfo product={product!} />
                </Box>
              </Stack>
            </Grid>
            <Grid item xs={12} md={7} lg={8}>
              <MainCard sx={{ maxHeight: '100%', display: 'flex', flexDirection: 'column' }}>
                <Stack spacing={3} sx={{ flex: 1, overflow: 'hidden' }}>
                  <Box>
                    <Tabs
                      value={value}
                      indicatorColor="primary"
                      onChange={handleChange}
                      aria-label="product description tabs"
                      variant="scrollable"
                      sx={{
                        '& .MuiTab-root': {
                          minWidth: 'auto',
                          px: 2,
                          py: 1,
                          fontSize: '0.875rem',
                          fontWeight: 500,
                          textTransform: 'none'
                        }
                      }}
                    >
                      <Tab label="Variants" {...a11yProps(0)} />
                      <Tab label="Customisations" {...a11yProps(1)} />
                      <Tab label="Specifications" {...a11yProps(2)} />

                      <Tab label="Details" {...a11yProps(2)} />
                      <Tab label="Disclaimers" {...a11yProps(3)} />

                      <Tab label="Return Policy" {...a11yProps(4)} />
                      <Tab label="Terms & Conditions" {...a11yProps(5)} />
                    </Tabs>
                    <Divider />
                  </Box>
                  <Box
                    sx={{
                      flex: 1,
                      overflow: 'auto',
                      pr: 1,
                      '&::-webkit-scrollbar': {
                        width: '6px'
                      },
                      '&::-webkit-scrollbar-thumb': {
                        backgroundColor: 'divider',
                        borderRadius: '3px'
                      }
                    }}
                  >
                    <TabPanel value={value} index={0}>
                      <Box>
                        <ProductDetails product={product!} />
                      </Box>
                    </TabPanel>
                    <TabPanel value={value} index={1}>
                      <Box
                        sx={{
                          p: 3,
                          height: '460px',
                          overflow: 'auto',
                          border: '1px solid',
                          borderColor: 'divider',
                          borderRadius: 1,
                          backgroundColor: 'background.paper',
                          '&::-webkit-scrollbar': {
                            width: '6px'
                          },
                          '&::-webkit-scrollbar-thumb': {
                            backgroundColor: 'divider',
                            borderRadius: '3px'
                          },
                          '&::-webkit-scrollbar-track': {
                            backgroundColor: 'transparent'
                          }
                        }}
                      >
                        <ProductSpecifications product={product!} />
                      </Box>
                    </TabPanel>
                    <TabPanel value={value} index={2}>
                      <Box
                        sx={{
                          p: 3,
                          height: '460px',
                          overflow: 'auto',
                          border: '1px solid',
                          borderColor: 'divider',
                          borderRadius: 1,
                          backgroundColor: 'background.paper',
                          '&::-webkit-scrollbar': {
                            width: '6px'
                          },
                          '&::-webkit-scrollbar-thumb': {
                            backgroundColor: 'divider',
                            borderRadius: '3px'
                          },
                          '&::-webkit-scrollbar-track': {
                            backgroundColor: 'transparent'
                          }
                        }}
                      >
                        <Typography
                          color="text.secondary"
                          sx={{
                            whiteSpace: 'pre-line',
                            lineHeight: 1.7
                          }}
                        >
                          {product?.productReturnPolicy?.returnPolicy
                            ? renderRichText(product.productReturnPolicy.returnPolicy)
                            : 'No data found'}
                        </Typography>
                      </Box>
                    </TabPanel>
                    <TabPanel value={value} index={3}>
                      <Box
                        sx={{
                          p: 3,
                          height: '460px',
                          overflow: 'auto',
                          border: '1px solid',
                          borderColor: 'divider',
                          borderRadius: 1,
                          backgroundColor: 'background.paper',
                          '&::-webkit-scrollbar': {
                            width: '6px'
                          },
                          '&::-webkit-scrollbar-thumb': {
                            backgroundColor: 'divider',
                            borderRadius: '3px'
                          },
                          '&::-webkit-scrollbar-track': {
                            backgroundColor: 'transparent'
                          }
                        }}
                      >
                        <Typography
                          color="text.secondary"
                          sx={{
                            whiteSpace: 'pre-line',
                            lineHeight: 1.7
                          }}
                        >
                          {product?.productTermsAndCondition?.terms
                            ? renderRichText(product.productTermsAndCondition.terms)
                            : 'No data found'}
                        </Typography>
                      </Box>
                    </TabPanel>
                    <TabPanel value={value} index={4}>
                      <Box
                        sx={{
                          p: 3,
                          height: '460px',
                          overflow: 'auto',
                          border: '1px solid',
                          borderColor: 'divider',
                          borderRadius: 1,
                          backgroundColor: 'background.paper',
                          '&::-webkit-scrollbar': {
                            width: '6px'
                          },
                          '&::-webkit-scrollbar-thumb': {
                            backgroundColor: 'divider',
                            borderRadius: '3px'
                          },
                          '&::-webkit-scrollbar-track': {
                            backgroundColor: 'transparent'
                          }
                        }}
                      >
                        <Typography
                          color="text.secondary"
                          sx={{
                            whiteSpace: 'pre-line',
                            lineHeight: 1.7
                          }}
                        >
                          {product?.productDisclaimer && (product.productDisclaimer as ProductDisclaimer).disclaimer
                            ? renderRichText((product.productDisclaimer as ProductDisclaimer).disclaimer)
                            : 'No data found'}
                        </Typography>
                      </Box>
                    </TabPanel>
                    <TabPanel value={value} index={5}>
                      <Box
                        sx={{
                          p: 3,
                          height: '460px',
                          overflow: 'auto',
                          border: '1px solid',
                          borderColor: 'divider',
                          borderRadius: 1,
                          backgroundColor: 'background.paper',
                          '&::-webkit-scrollbar': {
                            width: '6px'
                          },
                          '&::-webkit-scrollbar-thumb': {
                            backgroundColor: 'divider',
                            borderRadius: '3px'
                          },
                          '&::-webkit-scrollbar-track': {
                            backgroundColor: 'transparent'
                          }
                        }}
                      >
                        <ProductVariantsList refetch={refetch} productVariants={product?.productVariants ?? []} />
                      </Box>
                    </TabPanel>
                    <TabPanel value={value} index={6}>
                      <Box
                        sx={{
                          p: 3,
                          height: '460px',
                          overflow: 'auto',
                          border: '1px solid',
                          borderColor: 'divider',
                          borderRadius: 1,
                          backgroundColor: 'background.paper',
                          '&::-webkit-scrollbar': {
                            width: '6px'
                          },
                          '&::-webkit-scrollbar-thumb': {
                            backgroundColor: 'divider',
                            borderRadius: '3px'
                          },
                          '&::-webkit-scrollbar-track': {
                            backgroundColor: 'transparent'
                          }
                        }}
                      >
                        <ProductCustomizationFieldsTable productCustomizationFields={product?.productCustomizationFields ?? []} />
                      </Box>
                    </TabPanel>
                  </Box>
                </Stack>
              </MainCard>
            </Grid>
          </Grid>
        )}
      </Grid>
    </Grid>
  );
}
