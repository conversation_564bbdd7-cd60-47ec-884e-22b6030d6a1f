import React, { useState } from 'react';
import { Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Typography, Paper, IconButton, Stack } from '@mui/material';
import { ProductVariant } from 'types/product';
import { Edit, Trash } from 'iconsax-react';
import { Tooltip, Dialog, DialogTitle, DialogContent, DialogActions, Box } from '@mui/material';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { openSnackbar } from 'api/snackbar';
import { SnackbarProps } from 'types/snackbar';
import { Button } from '@mui/material';
import { useDeleteProductVariantByIdMutation } from 'redux/app/products/productApiSlice';

export default function ProductVariantsTable({
  productVariants,
  hideTitle,
  callback,
  refetch
}: {
  productVariants: ProductVariant[];
  hideTitle?: boolean;
  callback?: string;
  refetch: () => void;
}) {
  const router = useRouter();
  const [deleteProductVariantById] = useDeleteProductVariantByIdMutation();
  const [openDeleteModal, setOpenDeleteModal] = useState(false);
  const [selectedVariantId, setSelectedVariantId] = useState<string | null>(null);
  const handleDelete = async () => {
    if (selectedVariantId) {
      await deleteProductVariantById(selectedVariantId).unwrap();
      openSnackbar({
        open: true,
        message: 'Product Variant deleted successfully.',
        variant: 'alert',
        alert: { color: 'success' }
      } as SnackbarProps);
      await refetch();

      setOpenDeleteModal(false);
    }
  };
  if (!productVariants?.length) {
    return (
      <Typography variant="body2" color="text.secondary" sx={{ ml: 4 }}>
        <strong> No data found</strong>
      </Typography>
    );
  }

  return (
    <>
      {!hideTitle && (
        <Typography variant="h5" gutterBottom>
          Product Variants
        </Typography>
      )}
      <TableContainer component={Paper} elevation={1}>
        <Table size="small">
          <TableHead>
            <TableRow>
              <TableCell>
                <strong>Image</strong>
              </TableCell>
              <TableCell>
                <strong>Name</strong>
              </TableCell>
              <TableCell>
                <strong>SKU</strong>
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {productVariants.map((variant) => (
              <TableRow key={variant.id}>
                <TableCell>
                  <Image
                    src={variant.featuredAsset?.previewUrl}
                    alt="Product"
                    width={40}
                    height={40}
                    style={{
                      objectFit: 'cover',
                      borderRadius: 4,
                      marginRight: 26
                    }}
                  />
                </TableCell>
                <TableCell>{variant.name}</TableCell>
                <TableCell>{variant.sku}</TableCell>
                <TableCell>
                  <Stack direction="row" spacing={1}>
                    <Tooltip title="Edit Product Variant">
                      <IconButton
                        color="primary"
                        onClick={() => {
                          router.push(`/products/variants/${variant.id}?callback=${callback ?? '/products'}`);
                        }}
                      >
                        <Edit />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Delete Product Variant">
                      <IconButton
                        color="error"
                        onClick={() => {
                          setSelectedVariantId(variant.id);
                          setOpenDeleteModal(true);
                        }}
                      >
                        <Trash />
                      </IconButton>
                    </Tooltip>
                  </Stack>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
      <Dialog open={openDeleteModal} onClose={() => setOpenDeleteModal(false)} aria-labelledby="delete-confirmation-dialog">
        <DialogTitle id="delete-confirmation-dialog">
          <Typography variant="h6" sx={{ fontWeight: 600, color: 'text.primary' }}>
            Delete Product Variant
          </Typography>
        </DialogTitle>
        <DialogContent>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Typography variant="h6">
              Are you sure you want to permanently delete this product variant? Deleting this variant will remove it from your store, and
              customers will no longer be able to purchase it.
            </Typography>
          </Box>
        </DialogContent>
        <DialogActions sx={{ justifyContent: 'flex-end' }}>
          <Button onClick={() => setOpenDeleteModal(false)} color="primary" variant="outlined" sx={{ width: 120 }}>
            Cancel
          </Button>
          <Button onClick={handleDelete} color="error" variant="contained" sx={{ width: 120 }}>
            Yes, Delete
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
}
