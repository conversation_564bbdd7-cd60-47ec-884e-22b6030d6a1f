import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import React from 'react';

interface InfoItem {
  label: string;
  value: string | React.ReactNode;
}

export function InfoGroup({ items, title }: { items: InfoItem[]; title?: string }) {
  if (items.length === 0) return null;

  return (
    <Grid container spacing={2} sx={{ mt: 2 }}>
      {title && (
        <Grid item xs={12}>
          <Typography variant="h5">{title}</Typography>
        </Grid>
      )}
      {items.map((item, index) => (
        <React.Fragment key={index}>
          <Grid item xs={4}>
            <Typography color="text.secondary" noWrap>
              {item.label}:
            </Typography>
          </Grid>
          <Grid item xs={8}>
            <Typography
              sx={{
                whiteSpace: 'normal',
                wordWrap: 'break-word'
              }}
            >
              {item.value}
            </Typography>
          </Grid>
        </React.Fragment>
      ))}
    </Grid>
  );
}
