'use client';

import { useMemo, useState, MouseEvent, useCallback, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useTheme } from '@mui/material/styles';
import Stack from '@mui/material/Stack';
import Tooltip from '@mui/material/Tooltip';
import Typography from '@mui/material/Typography';
import { SortingState, ColumnFiltersState, ColumnDef, PaginationState } from '@tanstack/react-table';
import IconButton from '../../components/@extended/IconButton';
import { Add, Edit, Eye, Trash } from 'iconsax-react';
import moment from 'moment-timezone';
import { convertFiltersToLoopbackWhere, convertPaginationToLoopback, convertSortingToLoopbackSort } from '../../utils/table-filter';
import Loader from 'components/Loader';

import { ThemeMode } from 'config';
import { Chip } from '@mui/material';

import { PermissionKeys } from 'enums/permission-keys.enum';
import { useAuth } from 'contexts/AuthContext';
import withPermission from 'hoc/withPermission';
import AlertFacetDelete from './AlertFacetDelete';
import FacetTable from './FacetTable';
import { useGetFacetsCountQuery, useGetFacetsQuery } from 'redux/app/facet/facetApiSlice';
import { Facet } from 'types/facet';
import IndeterminateCheckbox from 'components/third-party/react-table/IndeterminateCheckbox';

const FacetListPage = () => {
  const theme = useTheme();
  const router = useRouter();
  const { hasPermission } = useAuth();

  const [sorting, setSorting] = useState<SortingState>([{ id: 'createdOn', desc: true }]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [globalFilter, setGlobalFilter] = useState<string>('');
  const [pagination, setPagination] = useState<PaginationState>({ pageIndex: 0, pageSize: 10 });
  const [facetDeleteId, setFacetDeleteId] = useState<string>('');

  const canCreate = hasPermission(PermissionKeys.CreateFacet);
  const canEdit = hasPermission(PermissionKeys.UpdateFacet);
  const canDelete = hasPermission(PermissionKeys.DeleteFacet);

  const [open, setOpen] = useState<boolean>(false);
  const {
    data: facetList,
    isLoading: facetListLoading,
    refetch
  } = useGetFacetsQuery({
    order: convertSortingToLoopbackSort(sorting),
    where: convertFiltersToLoopbackWhere(columnFilters, globalFilter, ['name']),
    include: [{ relation: 'facetValues' }],
    ...convertPaginationToLoopback(pagination)
  });

  const { data: facetCount, isLoading: facetCountLoading } = useGetFacetsCountQuery({
    where: convertFiltersToLoopbackWhere(columnFilters, globalFilter, ['name']),
    include: []
  });

  const handleClose = useCallback(() => setOpen((prev) => !prev), []);

  useEffect(() => {
    refetch?.();
  }, [refetch]);

  const columns = useMemo<ColumnDef<Facet>[]>(
    () => [
      {
        id: 'Row Selection',
        header: ({ table }) => (
          <IndeterminateCheckbox
            checked={table.getIsAllRowsSelected()}
            indeterminate={table.getIsSomeRowsSelected()}
            onChange={table.getToggleAllRowsSelectedHandler()}
          />
        ),
        cell: ({ row }) => (
          <IndeterminateCheckbox
            checked={row.getIsSelected()}
            disabled={!row.getCanSelect()}
            indeterminate={row.getIsSomeSelected()}
            onChange={row.getToggleSelectedHandler()}
          />
        )
      },
      {
        header: 'Name',
        accessorKey: 'name',
        enableSorting: true,
        cell: ({ row }) => <Typography>{row.original?.name ?? '-'}</Typography>
      },
      {
        header: 'Values',
        accessorKey: 'values',
        cell: ({ row }) => {
          const values = row.original?.facetValues || [];
          return (
            <Stack direction="row" spacing={1} sx={{ flexWrap: 'wrap', gap: 1 }}>
              {values.slice(0, 3).map((value, index) => (
                <Chip key={index} label={value.name} size="small" variant="outlined" />
              ))}
              {values.length > 3 && <Chip label={`+${values.length - 3} more`} size="small" variant="outlined" color="default" />}
            </Stack>
          );
        }
      },
      {
        header: 'Created On',
        accessorKey: 'createdOn',
        enableSorting: true,
        cell: ({ row }) => <Typography>{moment(row.original?.createdOn).format('DD-MM-YYYY') ?? '-'}</Typography>
      },
      {
        header: 'Actions',
        enableSorting: false,
        cell: ({ row }) => {
          const collapseIcon =
            row.getCanExpand() && row.getIsExpanded() ? (
              <Add
                style={{
                  color: theme.palette.error.main,
                  transform: 'rotate(45deg)'
                }}
              />
            ) : (
              <Eye />
            );

          return (
            <Stack direction="row" alignItems="center" justifyContent="flex-start" spacing={0}>
              <Tooltip title="View">
                <IconButton
                  onClick={row.getToggleExpandedHandler()}
                  sx={{
                    color: theme.palette.primary.light,
                    ':hover': {
                      backgroundColor: theme.palette.mode === ThemeMode.DARK ? theme.palette.common.white : theme.palette.primary.light
                    }
                  }}
                >
                  {collapseIcon}
                </IconButton>
              </Tooltip>
              <Tooltip title="Edit">
                <IconButton
                  onClick={(e: MouseEvent<HTMLButtonElement>) => {
                    e.stopPropagation();
                    router.push(`/facets/edit/${row.original.id}`);
                  }}
                  disabled={!canEdit}
                  sx={{
                    ':hover': {
                      backgroundColor: theme.palette.mode === ThemeMode.DARK ? theme.palette.common.white : theme.palette.primary.light
                    }
                  }}
                >
                  <Edit />
                </IconButton>
              </Tooltip>

              <Tooltip title="Delete">
                <IconButton
                  color="error"
                  sx={{
                    ':hover': {
                      color: theme.palette.mode === ThemeMode.DARK ? theme.palette.common.white : theme.palette.error[100]
                    }
                  }}
                  onClick={(e: MouseEvent<HTMLButtonElement>) => {
                    e.stopPropagation();
                    handleClose();
                    setFacetDeleteId(row.original.id as string);
                  }}
                  disabled={!canDelete}
                >
                  <Trash />
                </IconButton>
              </Tooltip>
            </Stack>
          );
        }
      }
    ],
    [theme, router, canEdit, canDelete, handleClose]
  );

  return (
    <>
      {facetCountLoading || facetListLoading ? (
        <Loader />
      ) : (
        <FacetTable
          {...{
            data: facetList as Facet[],
            columns,
            setSorting,
            sorting,
            columnFilters,
            setColumnFilters,
            loading: facetListLoading,
            globalFilter,
            setGlobalFilter,
            pagination,
            setPagination,
            totalRows: facetCount?.count ?? 0,
            canCreate,
            refetch
          }}
        />
      )}
      <AlertFacetDelete refetch={refetch} id={facetDeleteId} title={facetDeleteId} open={open} handleClose={handleClose} />
    </>
  );
};

export default withPermission(PermissionKeys.ViewFacet)(FacetListPage);
