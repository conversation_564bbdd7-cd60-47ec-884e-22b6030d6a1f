'use client';

import React, { useState } from 'react';
import { <PERSON>ton, Chip, Grid, Stack, TextField, Typography, Autocomplete, Paper } from '@mui/material';
import { useFormik } from 'formik';
import { useRouter } from 'next/navigation';
import { FacetSchema } from '../../../validations/facet';
import { openSnackbar } from 'api/snackbar';
import { SnackbarProps } from 'types/snackbar';
import { useCreateFacetMutation, useUpdateFacetMutation } from 'redux/app/facet/facetApiSlice';

interface OptionValue {
  name: string;
}

interface FacetFormValues {
  name: string;
  values: OptionValue[];
}

interface FacetCreateProps {
  initialValues?: FacetFormValues;
  isEdit?: boolean;
  facetId?: string;
  refetch?: () => void;
}

const FacetCreate: React.FC<FacetCreateProps> = ({ initialValues, isEdit = false, facetId, refetch }) => {
  const [createFacet, { isLoading: creating }] = useCreateFacetMutation();
  const [updateFacet, { isLoading: updating }] = useUpdateFacetMutation();
  const [valuesTouched, setValuesTouched] = useState(false);
  const router = useRouter();

  const formik = useFormik<FacetFormValues>({
    initialValues: initialValues || {
      name: '',
      values: []
    },
    enableReinitialize: true,
    validationSchema: FacetSchema,
    onSubmit: async (values, { resetForm }) => {
      try {
        if (isEdit && facetId) {
          await updateFacet({ id: facetId, data: values }).unwrap();
        } else {
          await createFacet(values).unwrap();
        }
        refetch?.();
        resetForm();
        openSnackbar({
          open: true,
          message: isEdit ? 'Tag updated successfully' : 'Tag created successfully',
          variant: 'alert',
          alert: { color: 'success' }
        } as SnackbarProps);
        router.push('/facets');
      } catch (error: any) {
        const errorMessage = error?.data?.error?.message || error?.message || 'An error occurred';
        formik.setFieldError('name', errorMessage);
      }
    }
  });

  const handleValuesChange = (_: any, newValue: string[]) => {
    const formattedValues = newValue.map((val) => ({ name: val }));
    formik.setFieldValue('values', formattedValues);
    setValuesTouched(true);
  };

  const showValuesError = valuesTouched && formik.values.values.length === 0;
  const isSubmitting = creating || updating;

  return (
    <Paper sx={{ p: 4, mt: 4 }}>
      <Typography variant="h4" gutterBottom>
        {isEdit ? 'Edit Tag' : 'Create Tag'}
      </Typography>
      <form onSubmit={formik.handleSubmit}>
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Typography variant="h6" sx={{ mt: 2, mb: 1 }}>
              Name
            </Typography>
            <TextField
              fullWidth
              id="name"
              name="name"
              placeholder="Enter tag name"
              value={formik.values.name}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              error={formik.touched.name && Boolean(formik.errors.name)}
              helperText={formik.touched.name && formik.errors.name}
              sx={{
                mb: 2,
                '& .MuiOutlinedInput-root': {
                  borderRadius: '10px'
                }
              }}
            />
          </Grid>

          <Grid item xs={12}>
            <Typography variant="h6" sx={{ mb: 1 }}>
              Values
            </Typography>
            <Autocomplete
              multiple
              freeSolo
              options={[]}
              value={formik.values.values.map((v) => v.name)}
              onChange={handleValuesChange}
              onBlur={() => setValuesTouched(true)}
              renderTags={(value: readonly string[], getTagProps) =>
                value.map((option, index) => <Chip variant="outlined" label={option} {...getTagProps({ index })} key={index} />)
              }
              renderInput={(params) => (
                <TextField
                  {...params}
                  variant="outlined"
                  placeholder="Type the value and press enter."
                  error={showValuesError}
                  helperText={showValuesError ? 'At least one value is required' : ''}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: '10px'
                    }
                  }}
                />
              )}
            />
          </Grid>

          <Grid item xs={12}>
            <Stack direction="row" spacing={2} justifyContent="flex-end">
              {!isEdit && (
                <Button
                  type="button"
                  variant="outlined"
                  onClick={() => {
                    formik.resetForm({ values: formik.initialValues });
                    setValuesTouched(false);
                  }}
                  disabled={isSubmitting}
                >
                  Reset
                </Button>
              )}
              <Button type="submit" variant="contained" disabled={isSubmitting || !formik.isValid}>
                {isSubmitting ? (isEdit ? 'Updating...' : 'Creating...') : isEdit ? 'Update' : 'Create'}
              </Button>
            </Stack>
          </Grid>
        </Grid>
      </form>
    </Paper>
  );
};

export default FacetCreate;
