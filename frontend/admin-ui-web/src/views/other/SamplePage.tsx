'use client';

// material-ui
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import Stack from '@mui/material/Stack';
import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import ListItemText from '@mui/material/ListItemText';
import ListItemAvatar from '@mui/material/ListItemAvatar';
import Avatar from '@mui/material/Avatar';
import Chip from '@mui/material/Chip';
import Divider from '@mui/material/Divider';

// project-imports
import MainCard from 'components/MainCard';

// third-party
import moment from 'moment-timezone';

// redux
import { useGetproductCountQuery, useGetproductsQuery } from 'redux/app/products/productApiSlice';
import { useGetCustomerCountQuery, useGetCustomersQuery } from 'redux/app/customer/customerApiSlice';
import { useGetSellersCountQuery, useGetSellersQuery } from 'redux/app/seller/sellerApiSlice';
import { useGetAdminsCountQuery, useGetAdminsQuery } from 'redux/app/admin/adminApiSlice';
import { useGetOrderCountQuery, useGetOrderQuery } from 'redux/app/orders/orderApiSlice';
import { useGetReviewCountQuery, useGetReviewsQuery } from 'redux/app/reviews/reviewApiSlice';

// icons
import { People, UserSquare, Additem, SecurityUser, Receipt, Star1 } from 'iconsax-react';
import AnalyticsCard from 'components/cards/AnalyticsCard';

// ==============================|| ADMIN DASHBOARD ||============================== //

export default function SamplePage() {
  const userIncludes = [
    {
      relation: 'userTenant',
      required: true,
      scope: {
        include: [
          {
            relation: 'user',
            required: true
          }
        ]
      }
    }
  ];
  // Analytics - Total Counts Only
  const { data: totalProducts, isLoading: loadingTotalProducts } = useGetproductCountQuery({});
  const { data: totalCustomers, isLoading: loadingTotalCustomers } = useGetCustomerCountQuery({});
  const { data: totalSellers, isLoading: loadingTotalSellers } = useGetSellersCountQuery({});
  const { data: totalSubAdmins, isLoading: loadingTotalSubAdmins } = useGetAdminsCountQuery({});
  const { data: totalOrders, isLoading: loadingTotalOrders } = useGetOrderCountQuery();
  const { data: totalReviews, isLoading: loadingTotalReviews } = useGetReviewCountQuery({});

  // Recent Items for Analysis
  const { data: recentProducts } = useGetproductsQuery({
    order: ['createdOn DESC'],
    limit: 5
  });
  const { data: recentCustomers } = useGetCustomersQuery({
    order: ['createdOn DESC'],
    limit: 5,
    include: userIncludes
  });
  const { data: recentSellers } = useGetSellersQuery({
    order: ['createdOn DESC'],
    limit: 5,
    include: userIncludes
  });
  const { data: recentSubAdmins } = useGetAdminsQuery({
    limit: 5,
    order: ['createdOn DESC'],
    include: userIncludes
  });
  const { data: recentOrders } = useGetOrderQuery({
    filter: {
      order: ['modifiedOn DESC'],
      limit: 5,
      include: [
        {
          relation: 'productVariant',
          scope: {
            include: [
              {
                relation: 'product'
              }
            ]
          }
        }
      ]
    }
  });
  const { data: recentReviews } = useGetReviewsQuery({
    order: ['createdOn DESC'],
    limit: 5,
    include: [
      {
        relation: 'productVariant',
        scope: {
          include: [
            {
              relation: 'product'
            }
          ]
        }
      },
      {
        relation: 'customer',
        scope: {
          include: [
            {
              relation: 'userTenant',
              scope: {
                include: [
                  {
                    relation: 'user'
                  }
                ]
              }
            }
          ]
        }
      }
    ]
  });

  return (
    <Box>
      <Stack spacing={3}>
        {/* Header */}
        <Box>
          <Typography variant="body1" color="textSecondary">
            Welcome to Ecomdukes Admin Panel. Monitor your platform&apos;s key metrics and analytics.
          </Typography>
        </Box>

        {/* Platform Overview */}
        <Box>
          <Typography variant="h5" gutterBottom sx={{ mb: 2 }}>
            Platform Overview
          </Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} sm={6} md={2.4}>
              <AnalyticsCard
                title="Total Products"
                count={totalProducts?.count || 0}
                isLoading={loadingTotalProducts}
                color="primary"
                icon={<Additem size={24} />}
                subtitle="All products"
              />
            </Grid>
            <Grid item xs={12} sm={6} md={2.4}>
              <AnalyticsCard
                title="Total Customers"
                count={totalCustomers?.count || 0}
                isLoading={loadingTotalCustomers}
                color="info"
                icon={<People size={24} />}
                subtitle="All customers"
              />
            </Grid>
            <Grid item xs={12} sm={6} md={2.4}>
              <AnalyticsCard
                title="Total Sellers"
                count={totalSellers?.count || 0}
                isLoading={loadingTotalSellers}
                color="secondary"
                icon={<UserSquare size={24} />}
                subtitle="All sellers"
              />
            </Grid>
            <Grid item xs={12} sm={6} md={2.4}>
              <AnalyticsCard
                title="Total Orders"
                count={totalOrders?.count || 0}
                isLoading={loadingTotalOrders}
                color="success"
                icon={<Receipt size={24} />}
                subtitle="All orders"
              />
            </Grid>
            <Grid item xs={12} sm={6} md={2.4}>
              <AnalyticsCard
                title="Total Reviews"
                count={totalReviews?.count || 0}
                isLoading={loadingTotalReviews}
                color="warning"
                icon={<Star1 size={24} />}
                subtitle="All reviews"
              />
            </Grid>
          </Grid>
        </Box>

        {/* Sub-Admin Analytics */}
        <Box>
          <Typography variant="h5" gutterBottom sx={{ mb: 2 }}>
            Administration
          </Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} sm={6} md={6}>
              <AnalyticsCard
                title="Total Sub-Admins"
                count={totalSubAdmins?.count || 0}
                isLoading={loadingTotalSubAdmins}
                color="secondary"
                icon={<SecurityUser size={24} />}
                subtitle="All sub-admin accounts"
              />
            </Grid>
          </Grid>
        </Box>

        {/* Recent Items Analysis */}
        <Box>
          <Typography variant="h5" gutterBottom sx={{ mb: 2 }}>
            Recent Activity
          </Typography>
          <Grid container spacing={3}>
            {/* Recent Products */}
            <Grid item xs={12} md={6}>
              <MainCard title="Recent Products" content={false}>
                <List sx={{ py: 0 }}>
                  {recentProducts?.slice(0, 5).map((product, index) => (
                    <div key={product.id}>
                      <ListItem>
                        <ListItemAvatar>
                          <Avatar sx={{ bgcolor: 'primary.main' }}>{product.name?.charAt(0).toUpperCase()}</Avatar>
                        </ListItemAvatar>
                        <ListItemText primary={product.name} secondary={`Created: ${moment(product.createdOn).format('MMM DD, YYYY')}`} />
                        <Chip
                          label={product.status}
                          color={product.status === 'APPROVED' ? 'success' : product.status === 'REJECTED' ? 'error' : 'warning'}
                          size="small"
                        />
                      </ListItem>
                      {index < 4 && <Divider />}
                    </div>
                  ))}
                  {(!recentProducts || recentProducts.length === 0) && (
                    <ListItem>
                      <ListItemText primary="No recent products" />
                    </ListItem>
                  )}
                </List>
              </MainCard>
            </Grid>

            {/* Recent Customers */}
            <Grid item xs={12} md={6}>
              <MainCard title="Recent Customers" content={false}>
                <List sx={{ py: 0 }}>
                  {recentCustomers?.slice(0, 5).map((customer, index) => (
                    <div key={customer.id}>
                      <ListItem>
                        <ListItemAvatar>
                          <Avatar sx={{ bgcolor: 'info.main' }}>{customer.userTenant?.user?.firstName?.charAt(0).toUpperCase()}</Avatar>
                        </ListItemAvatar>
                        <ListItemText
                          primary={`${customer.userTenant?.user?.firstName} ${customer.userTenant?.user?.lastName}`}
                          secondary={`Joined: ${moment(customer.createdOn).format('MMM DD, YYYY')}`}
                        />
                        <Chip
                          label={customer.status || 'ACTIVE'}
                          color={customer.status === 'ACTIVE' ? 'success' : 'default'}
                          size="small"
                        />
                      </ListItem>
                      {index < 4 && <Divider />}
                    </div>
                  ))}
                  {(!recentCustomers || recentCustomers.length === 0) && (
                    <ListItem>
                      <ListItemText primary="No recent customers" />
                    </ListItem>
                  )}
                </List>
              </MainCard>
            </Grid>
          </Grid>

          {/* Second Row - Recent Sellers and Sub-Admins */}
          <Grid container spacing={3} sx={{ mt: 1 }}>
            {/* Recent Sellers */}
            <Grid item xs={12} md={6}>
              <MainCard title="Recent Sellers" content={false}>
                <List sx={{ py: 0 }}>
                  {recentSellers?.slice(0, 5).map((seller, index) => (
                    <div key={seller.id}>
                      <ListItem>
                        <ListItemAvatar>
                          <Avatar sx={{ bgcolor: 'secondary.main' }}>{seller.userTenant?.user?.firstName?.charAt(0).toUpperCase()}</Avatar>
                        </ListItemAvatar>
                        <ListItemText
                          primary={`${seller.userTenant?.user?.firstName} ${seller.userTenant?.user?.lastName}`}
                          secondary={`Registered: ${moment(seller.userTenant?.createdOn).format('MMM DD, YYYY')}`}
                        />
                        <Chip
                          label={seller.status}
                          color={seller.status === 'APPROVED' ? 'success' : seller.status === 'REJECTED' ? 'error' : 'warning'}
                          size="small"
                        />
                      </ListItem>
                      {index < 4 && <Divider />}
                    </div>
                  ))}
                  {(!recentSellers || recentSellers.length === 0) && (
                    <ListItem>
                      <ListItemText primary="No recent sellers" />
                    </ListItem>
                  )}
                </List>
              </MainCard>
            </Grid>

            {/* Recent Sub-Admins */}
            <Grid item xs={12} md={6}>
              <MainCard title="Recent Sub-Admins" content={false}>
                <List sx={{ py: 0 }}>
                  {recentSubAdmins?.slice(0, 5).map((admin, index) => (
                    <div key={admin.id}>
                      <ListItem>
                        <ListItemAvatar>
                          <Avatar sx={{ bgcolor: 'warning.main' }}>{admin.userTenant?.user?.firstName?.charAt(0).toUpperCase()}</Avatar>
                        </ListItemAvatar>
                        <ListItemText
                          primary={`${admin.userTenant?.user?.firstName} ${admin.userTenant?.user?.lastName}`}
                          secondary={`Added: ${moment(admin.createdOn).format('MMM DD, YYYY')}`}
                        />
                        <Chip label={admin.status || 'ACTIVE'} color={admin.status === 'ACTIVE' ? 'success' : 'default'} size="small" />
                      </ListItem>
                      {index < 4 && <Divider />}
                    </div>
                  ))}
                  {(!recentSubAdmins || recentSubAdmins.length === 0) && (
                    <ListItem>
                      <ListItemText primary="No recent sub-admins" />
                    </ListItem>
                  )}
                </List>
              </MainCard>
            </Grid>
          </Grid>

          {/* Third Row - Recent Orders and Reviews */}
          <Grid container spacing={3} sx={{ mt: 1 }}>
            {/* Recent Orders */}
            <Grid item xs={12} md={6}>
              <MainCard title="Recent Orders" content={false}>
                <List sx={{ py: 0 }}>
                  {recentOrders?.slice(0, 5).map((orderItem, index) => (
                    <div key={orderItem.id}>
                      <ListItem>
                        <ListItemAvatar>
                          <Avatar sx={{ bgcolor: 'success.main' }}>
                            <Receipt size={20} />
                          </Avatar>
                        </ListItemAvatar>
                        <ListItemText
                          primary={`${orderItem.productVariant?.product?.name || 'Unknown Product'}`}
                          secondary={`Order ID: ${orderItem.orderId} • Qty: ${orderItem.quantity}`}
                        />
                        <Chip
                          label={orderItem.status}
                          color={orderItem.status === 'DELIVERED' ? 'success' : orderItem.status === 'CANCELLED' ? 'error' : 'info'}
                          size="small"
                        />
                      </ListItem>
                      {index < 4 && <Divider />}
                    </div>
                  ))}
                  {(!recentOrders || recentOrders.length === 0) && (
                    <ListItem>
                      <ListItemText primary="No recent orders" />
                    </ListItem>
                  )}
                </List>
              </MainCard>
            </Grid>

            {/* Recent Reviews */}
            <Grid item xs={12} md={6}>
              <MainCard title="Recent Reviews" content={false}>
                <List sx={{ py: 0 }}>
                  {recentReviews?.slice(0, 5).map((review, index) => (
                    <div key={review.id}>
                      <ListItem>
                        <ListItemAvatar>
                          <Avatar sx={{ bgcolor: 'warning.main' }}>
                            <Star1 size={20} />
                          </Avatar>
                        </ListItemAvatar>
                        <ListItemText
                          primary={`${review.productVariant?.product?.name || 'Unknown Product'}`}
                          secondary={`By: ${review.customer?.userTenant?.user?.firstName || 'Anonymous'} • ${moment(review.createdOn).format('MMM DD, YYYY')}`}
                        />
                        <Chip
                          label={`${review.rating || 0}★`}
                          color={review.rating >= 4 ? 'success' : review.rating >= 3 ? 'warning' : 'error'}
                          size="small"
                        />
                      </ListItem>
                      {index < 4 && <Divider />}
                    </div>
                  ))}
                  {(!recentReviews || recentReviews.length === 0) && (
                    <ListItem>
                      <ListItemText primary="No recent reviews" />
                    </ListItem>
                  )}
                </List>
              </MainCard>
            </Grid>
          </Grid>
        </Box>
      </Stack>
    </Box>
  );
}
