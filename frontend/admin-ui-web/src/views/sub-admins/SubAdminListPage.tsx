'use client';

import { useMemo, useState, MouseEvent, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { useTheme } from '@mui/material/styles';
import Stack from '@mui/material/Stack';
import Tooltip from '@mui/material/Tooltip';
import Typography from '@mui/material/Typography';
import { SortingState, ColumnFiltersState, ColumnDef, PaginationState } from '@tanstack/react-table';
import IconButton from '../../components/@extended/IconButton';
import { DEFAULT_DATE_FORMAT, ThemeMode } from 'config';
import { Edit, Eye, Trash, ArrowLeft, Add } from 'iconsax-react';
import moment from 'moment-timezone';
import {
  useGetAdminsCountQuery,
  useGetAdminsQuery,
  useRemoveAdminMutation,
  useUpdateAdminStatusMutation
} from '../../redux/app/admin/adminApiSlice';
import IndeterminateCheckbox from '../../components/third-party/react-table/IndeterminateCheckbox';
import { SubAdmin } from 'types/admin';
import SubAdminView from './SubAdminView';
import Loader from 'components/Loader';
import withPermission from 'hoc/withPermission';
import { PermissionKeys } from 'enums/permission-keys.enum';
import { useAuth } from 'contexts/AuthContext';
import SubAdminTable from './SubAdminTable ';
import { convertFiltersToLoopbackWhere, convertPaginationToLoopback, convertSortingToLoopbackSort } from 'utils/table-filter';
import { openSnackbar } from 'api/snackbar';
import { SnackbarProps } from 'types/snackbar';
import { Avatar, Chip, Button } from '@mui/material';
import AdminStatusDialog from 'components/AdminStatusDialog';

const SubAdminListPage = () => {
  const theme = useTheme();
  const router = useRouter();
  const { hasPermission } = useAuth();

  const [sorting, setSorting] = useState<SortingState>([{ id: 'createdOn', desc: true }]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [globalFilter, setGlobalFilter] = useState<string>('');
  const [pagination, setPagination] = useState<PaginationState>({ pageIndex: 0, pageSize: 10 });

  const canCreate = hasPermission(PermissionKeys.CreateSeller);
  const canEdit = hasPermission(PermissionKeys.UpdateSeller);
  const canDelete = hasPermission(PermissionKeys.DeleteSeller);

  const [, setOpen] = useState<boolean>(false);

  const handleClose = useCallback(() => {
    setOpen((prev) => !prev);
  }, []);

  const {
    data: adminList,
    isLoading: adminListLoading,
    refetch
  } = useGetAdminsQuery({
    order: convertSortingToLoopbackSort(sorting),
    where: convertFiltersToLoopbackWhere(columnFilters, '', ['adminId']),
    ...convertPaginationToLoopback(pagination),
    include: [
      {
        relation: 'userTenant',
        required: true,
        scope: {
          include: [
            {
              relation: 'user',
              scope: globalFilter
                ? {
                    where: {
                      or: [
                        {
                          firstName: { ilike: `%${globalFilter}%` }
                        },
                        { lastName: { ilike: `%${globalFilter}%` } },
                        { email: { ilike: `%${globalFilter}%` } }
                      ]
                    }
                  }
                : undefined,
              required: true
            },
            {
              relation: 'featuredAsset'
            }
          ]
        }
      }
    ]
  });

  const { data: adminCount, isLoading: adminCountLoading } = useGetAdminsCountQuery({});

  const [removeAdmin] = useRemoveAdminMutation();
  const [updateAdminStatus, { isLoading: isUpdatingStatus }] = useUpdateAdminStatusMutation();
  const [selectedSubAdmin, setSelectedSubAdmin] = useState<SubAdmin | null>(null);
  const [statusDialogOpen, setStatusDialogOpen] = useState(false);
  const [statusUpdateData, setStatusUpdateData] = useState<{
    id: string;
    currentStatus: string;
    newStatus: string;
    adminName: string;
  } | null>(null);

  const handleDelete = useCallback(
    async (id: string) => {
      if (window.confirm('Are you sure you want to delete this admin?')) {
        await removeAdmin(id).unwrap();
        openSnackbar({
          open: true,
          message: 'Sub Admin deleted successfully',
          variant: 'alert',
          alert: {
            color: 'success'
          }
        } as SnackbarProps);
        refetch();
      }
    },
    [removeAdmin, refetch]
  );

  const handleStatusUpdate = useCallback((id: string, currentStatus: string, adminName: string) => {
    let newStatus: string;

    // Allow activation/deactivation for PENDING, APPROVED, and INACTIVE statuses
    if (currentStatus === 'APPROVED') {
      newStatus = 'INACTIVE';
    } else if (currentStatus === 'INACTIVE') {
      newStatus = 'APPROVED';
    } else if (currentStatus === 'PENDING') {
      newStatus = 'APPROVED'; // Default to approve for pending
    } else {
      return; // Don't allow status change for REJECTED
    }

    setStatusUpdateData({
      id,
      currentStatus,
      newStatus,
      adminName
    });
    setStatusDialogOpen(true);
  }, []);

  const handleConfirmStatusUpdate = useCallback(async () => {
    if (!statusUpdateData) return;

    const { id, newStatus } = statusUpdateData;
    const action = newStatus === 'INACTIVE' ? 'deactivate' : 'activate';

    await updateAdminStatus({ id, status: newStatus }).unwrap();
    openSnackbar({
      open: true,
      message: `Sub Admin ${action}d successfully`,
      variant: 'alert',
      alert: {
        color: 'success'
      }
    } as SnackbarProps);
    refetch();
    setStatusDialogOpen(false);
    setStatusUpdateData(null);
  }, [statusUpdateData, updateAdminStatus, refetch]);

  const handleCloseStatusDialog = useCallback(() => {
    setStatusDialogOpen(false);
    setStatusUpdateData(null);
  }, []);

  useEffect(() => {
    refetch();
  }, [refetch]);

  const columns = useMemo<ColumnDef<SubAdmin>[]>(
    () => [
      {
        id: 'Row Selection',
        header: ({ table }) => (
          <IndeterminateCheckbox
            checked={table.getIsAllRowsSelected()}
            indeterminate={table.getIsSomeRowsSelected()}
            onChange={table.getToggleAllRowsSelectedHandler()}
          />
        ),
        cell: ({ row }) => (
          <IndeterminateCheckbox
            checked={row.getIsSelected()}
            disabled={!row.getCanSelect()}
            indeterminate={row.getIsSomeSelected()}
            onChange={row.getToggleSelectedHandler()}
          />
        )
      },
      {
        header: 'Admin ID',
        accessorKey: 'adminId',
        enableSorting: true,
        enableColumnFilter: true,
        cell: ({ row }) => {
          const adminData = row.original;
          const imageUrl = adminData?.preSignedPhotoUrl;
          const adminId = adminData?.adminId;
          const firstName = adminData?.userTenant?.user?.firstName || '';
          const lastName = adminData?.userTenant?.user?.lastName || '';

          const initials = firstName || lastName ? `${firstName[0] || ''}${lastName[0] || ''}`.toUpperCase() : 'U';

          return (
            <div style={{ display: 'flex', alignItems: 'center', width: 150 }}>
              {imageUrl ? (
                <img
                  src={imageUrl}
                  alt="Admin"
                  style={{
                    width: 40,
                    height: 40,
                    objectFit: 'cover',
                    borderRadius: 4,
                    marginRight: 12
                  }}
                />
              ) : (
                <Avatar
                  alt="SubAdmin Avatar"
                  sx={{
                    width: 40,
                    height: 40,
                    marginRight: 1,
                    backgroundColor: theme.palette.primary.main,
                    color: theme.palette.primary.contrastText,
                    fontSize: '0.875rem' // optional: smaller initials text
                  }}
                >
                  {initials}
                </Avatar>
              )}
              <span>{adminId}</span>
            </div>
          );
        }
      },
      {
        header: 'First Name',
        accessorKey: 'user.firstName',
        enableColumnFilter: false,
        cell: ({ row }) => <Typography>{row.original?.userTenant?.user?.firstName ?? '-'}</Typography>
      },
      {
        header: 'Last Name',
        accessorKey: 'user.lastName',
        enableColumnFilter: false,
        cell: ({ row }) => <Typography>{row.original?.userTenant?.user?.lastName ?? '-'}</Typography>
      },
      {
        header: 'Email',
        accessorKey: 'user.email',
        enableColumnFilter: false,
        cell: ({ row }) => <Typography>{row.original?.userTenant?.user?.email ?? '-'}</Typography>
      },
      {
        header: 'Phone',
        accessorKey: 'user.phone',
        enableColumnFilter: false,
        cell: ({ row }) => <Typography>{row.original?.userTenant?.user?.phone ?? '-'}</Typography>
      },
      {
        header: 'Created On',
        accessorKey: 'createdOn',
        cell: ({ row }) => <Typography>{moment(row.original?.createdOn).format(DEFAULT_DATE_FORMAT) ?? '-'}</Typography>
      },
      {
        header: 'Status',
        accessorKey: 'status',
        enableColumnFilter: false,
        cell: ({ row }) => {
          const status = String(row.original?.status ?? 'PENDING');
          const getStatusColor = (status: string) => {
            switch (status) {
              case 'APPROVED':
                return 'success';
              case 'INACTIVE':
                return 'error';
              case 'PENDING':
                return 'warning';
              case 'REJECTED':
                return 'error';
              default:
                return 'default';
            }
          };

          return <Chip label={status} color={getStatusColor(status) as any} size="small" variant="filled" />;
        }
      },
      {
        header: 'Actions',
        meta: {
          className: 'cell-center'
        },
        enableSorting: false,
        cell: ({ row }) => {
          const collapseIcon =
            row.getCanExpand() && row.getIsExpanded() ? (
              <Add
                style={{
                  color: theme.palette.error.main,
                  transform: 'rotate(45deg)'
                }}
              />
            ) : (
              <Eye />
            );

          return (
            <Stack direction="row" alignItems="center" justifyContent="center" spacing={0}>
              <Tooltip title="View">
                <IconButton
                  color="secondary"
                  onClick={row.getToggleExpandedHandler()}
                  sx={{
                    color: theme.palette.primary.light,
                    ':hover': {
                      backgroundColor: theme.palette.mode === ThemeMode.DARK ? theme.palette.common.white : theme.palette.primary.light
                    }
                  }}
                >
                  {collapseIcon}
                </IconButton>
              </Tooltip>
              <Tooltip title="Edit">
                <IconButton
                  color="secondary"
                  onClick={(e: MouseEvent<HTMLButtonElement>) => {
                    e.stopPropagation();
                    router.push(`/sub-admins/edit/${row.original.id}`);
                  }}
                  disabled={!canEdit}
                  sx={{
                    color: theme.palette.primary.light,
                    ':hover': {
                      backgroundColor: theme.palette.mode === ThemeMode.DARK ? theme.palette.common.white : theme.palette.primary.light
                    }
                  }}
                >
                  <Edit />
                </IconButton>
              </Tooltip>

              <Tooltip title="Delete">
                <IconButton
                  color="error"
                  sx={{
                    ':hover': {
                      color: theme.palette.mode === ThemeMode.DARK ? theme.palette.common.white : theme.palette.error[100]
                    }
                  }}
                  onClick={(e: MouseEvent<HTMLButtonElement>) => {
                    e.stopPropagation();
                    handleClose();
                    handleDelete(row.original.id as string);
                  }}
                  disabled={!canDelete}
                >
                  <Trash />
                </IconButton>
              </Tooltip>
            </Stack>
          );
        }
      },
      {
        header: 'Activate/Deactivate',
        meta: {
          className: 'cell-center'
        },
        enableSorting: false,
        cell: ({ row }) => {
          return (
            <>
              {row.original?.status !== 'REJECTED' && (
                <Tooltip title={row.original?.status === 'APPROVED' ? 'Deactivate' : 'Activate'}>
                  <Button
                    variant="outlined"
                    size="small"
                    color={row.original?.status === 'APPROVED' ? 'error' : 'success'}
                    onClick={(e: MouseEvent<HTMLButtonElement>) => {
                      e.stopPropagation();
                      const adminName =
                        `${row.original?.userTenant?.user?.firstName || ''} ${row.original?.userTenant?.user?.lastName || ''}`.trim() ||
                        'Unknown Admin';
                      handleStatusUpdate(String(row.original.id), String(row.original?.status ?? 'PENDING'), adminName);
                    }}
                    disabled={!canEdit}
                    sx={{ minWidth: 'auto', px: 1, mr: 1 }}
                  >
                    {row.original?.status === 'APPROVED' ? 'Deactivate' : 'Activate'}
                  </Button>
                </Tooltip>
              )}
            </>
          );
        }
      }
    ],
    [theme, canDelete, canEdit, handleClose, handleDelete, handleStatusUpdate, router]
  );
  return (
    <>
      {selectedSubAdmin ? (
        <>
          <IconButton onClick={() => setSelectedSubAdmin(null)} sx={{ mb: 2 }}>
            <ArrowLeft />
          </IconButton>
          <SubAdminView data={selectedSubAdmin} />
        </>
      ) : adminCountLoading || adminListLoading ? (
        <Loader />
      ) : (
        <SubAdminTable
          {...{
            data: adminList || [],
            columns,
            setSorting,
            sorting,
            columnFilters,
            setColumnFilters,
            loading: adminListLoading,
            globalFilter,
            setGlobalFilter,
            pagination,
            setPagination,
            totalRows: adminCount?.count ?? 0,
            canCreate,
            refetch
          }}
        />
      )}

      {/* Status Update Dialog */}
      <AdminStatusDialog
        open={statusDialogOpen}
        onClose={handleCloseStatusDialog}
        onConfirm={handleConfirmStatusUpdate}
        adminName={statusUpdateData?.adminName || ''}
        currentStatus={statusUpdateData?.currentStatus || ''}
        newStatus={statusUpdateData?.newStatus || ''}
        isLoading={isUpdatingStatus}
      />
    </>
  );
};

export default withPermission(PermissionKeys.ViewSubAdmin)(SubAdminListPage);
