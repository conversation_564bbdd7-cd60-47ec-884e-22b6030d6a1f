import { <PERSON><PERSON>, TextField, Button, Box, Typography } from '@mui/material';
import MainCard from 'components/MainCard';
import { useFormik } from 'formik';
import { Dispatch, FC, SetStateAction, useEffect, useState } from 'react';
import { BannerMetadata, SectionItem } from 'types/cms';
import { bannerItemSchema } from '../../../validations/cms';
import SingleFileUpload from 'components/third-party/dropzone/SingleFileUpload';
import { Save2 } from 'iconsax-react';

interface Props {
  data: SectionItem;
  onChange: (value: SectionItem) => void;
  setFileMap: Dispatch<SetStateAction<Record<string, File>>>;
  fileMap: Record<string, File>;
}

export const BannerItem: FC<Props> = ({ data, onChange, setFileMap, fileMap }) => {
  const [localData, setLocalData] = useState<SectionItem>(data);
  const [isSaved, setIsSaved] = useState(true);
  const [localFileMap, setLocalFileMap] = useState<Record<string, File>>({ ...fileMap });
  // Update local data when props change
  useEffect(() => {
    setLocalData(data);
  }, [data]);

  useEffect(() => {
    setLocalFileMap({ ...fileMap });
  }, [fileMap]);

  const formik = useFormik<SectionItem>({
    initialValues: localData,
    enableReinitialize: true,
    validationSchema: bannerItemSchema,
    onSubmit: (values) => {
      onChange(values);
      setIsSaved(true);
      setFileMap({ ...localFileMap });
    }
  });

  const handleLocalChange = (field: string, value: any) => {
    formik.setFieldValue(field, value);
    setIsSaved(false);
  };

  const handleSave = () => {
    formik.handleSubmit();
  };

  return (
    <MainCard>
      <Grid container spacing={2}>
        <Grid item xs={12}>
          {/* Show existing image preview */}
          {data.previewUrl && !localFileMap[data.id ?? ''] && (
            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle2" sx={{ mb: 1 }}>
                Current Image:
              </Typography>
              <Box sx={{ position: 'relative', display: 'inline-block' }}>
                <img
                  src={data.previewUrl}
                  alt="Current image"
                  style={{
                    width: '300px',
                    height: '120px',
                    objectFit: 'fill',
                    borderRadius: '8px',
                    border: '1px solid #e0e0e0'
                  }}
                />
              </Box>
            </Box>
          )}

          <SingleFileUpload
            setFieldValue={(field: string, value: any): void => {
              handleLocalChange('imageUrl', value?.preview ?? '');
              setLocalFileMap({ ...fileMap, [data.id ?? '']: value });
            }}
            accept={{ 'image/*': ['.png', '.jpg', '.jpeg'] }}
            file={localFileMap[data.id ?? '']}
          />
          {formik.errors.imageUrl && <Box sx={{ color: 'error.main', mt: 1, fontSize: '0.75rem' }}>{formik.errors.imageUrl as string}</Box>}
        </Grid>
        <Grid item xs={12}>
          <TextField
            fullWidth
            name="title"
            label="Title"
            placeholder="Enter banner title"
            value={formik.values.title || ''}
            onChange={(e) => {
              handleLocalChange('title', e.target.value);
            }}
            error={Boolean(formik.errors.title)}
            helperText={formik.errors.title}
          />
        </Grid>
        <Grid item xs={12}>
          <TextField
            fullWidth
            name="subtitle"
            label="Subtitle"
            placeholder="Enter banner subtitle"
            value={formik.values.subtitle || ''}
            onChange={(e) => {
              handleLocalChange('subtitle', e.target.value);
            }}
            error={Boolean(formik.errors.subtitle)}
            helperText={formik.errors.subtitle}
          />
        </Grid>
        <Grid item xs={12}>
          <TextField
            fullWidth
            name="metadata.redirectUrl"
            label="Redirection URL"
            placeholder="Enter the redirection URL"
            value={(formik.values.metadata as BannerMetadata)?.redirectUrl || ''}
            onChange={(e) => {
              handleLocalChange('metadata', {
                ...formik.values.metadata,
                redirectUrl: e.target.value
              });
            }}
            error={Boolean((formik.errors.metadata as BannerMetadata)?.redirectUrl)}
            helperText={
              (formik.errors.metadata as BannerMetadata)?.redirectUrl ? (formik.errors.metadata as BannerMetadata).redirectUrl : ''
            }
          />
        </Grid>
        <Grid item xs={12}>
          <Box display="flex" justifyContent="flex-end">
            <Button
              variant="contained"
              color="primary"
              startIcon={<Save2 />}
              onClick={handleSave}
              disabled={isSaved || !formik.dirty || !formik.isValid}
            >
              {isSaved ? 'Saved' : 'Save'}
            </Button>
          </Box>
        </Grid>
      </Grid>
    </MainCard>
  );
};
