import { <PERSON>rid, TextField, Button, Box, Autocomplete, Typography } from '@mui/material';
import MainCard from 'components/MainCard';
import { useFormik } from 'formik';
import { Dispatch, FC, SetStateAction, useEffect, useState, useCallback } from 'react';
import { OccasionCardsMetadata, SectionItem } from 'types/cms';
import { occasionCardsItemSchema } from '../../../validations/cms';
import SingleFileUpload from 'components/third-party/dropzone/SingleFileUpload';
import { Save2 } from 'iconsax-react';
import debounce from 'lodash/debounce';
import { useLazyGetFacetsQuery } from 'redux/app/facet/facetApiSlice';

interface Props {
  data: SectionItem;
  onChange: (value: SectionItem) => void;
  setFileMap: Dispatch<SetStateAction<Record<string, File>>>;
  fileMap: Record<string, File>;
}

export const OccasionCardsItem: FC<Props> = ({ data, onChange, setFileMap, fileMap }) => {
  const [localData, setLocalData] = useState<SectionItem>(data);
  const [searchTerm, setSearchTerm] = useState('');
  const [facetOptions, setFacetOptions] = useState<any[]>([]);
  const [getFacets, { isLoading }] = useLazyGetFacetsQuery();

  // Update local data when props change
  useEffect(() => {
    setLocalData(data);
  }, [data]);

  // Create a debounced search function for categories
  const debouncedSearch = useCallback(
    debounce(async (term: string) => {
      if (term.length < 2) return;

      const result = await getFacets({
        where: { name: { ilike: `%${term}%` } },
        limit: 10
      }).unwrap();

      setFacetOptions(result || []);
    }, 500),
    [getFacets]
  );

  // Effect to trigger search when searchTerm changes
  useEffect(() => {
    if (searchTerm) {
      debouncedSearch(searchTerm);
    }

    // Cleanup function
    return () => {
      debouncedSearch.cancel();
    };
  }, [searchTerm, debouncedSearch]);

  // Load selected category on initial render
  useEffect(() => {
    const loadSelectedCategory = async () => {
      if (data.entityId) {
        const result = await getFacets({
          where: { id: data.entityId }
        }).unwrap();

        if (result && result.length > 0) {
          setFacetOptions((prevOptions) => {
            // Avoid duplicates
            if (!prevOptions.some((p) => p.id === result[0].id)) {
              return [...prevOptions, result[0]];
            }
            return prevOptions;
          });
        }
      }
    };

    loadSelectedCategory();
  }, [data.entityId, getFacets]);

  const formik = useFormik<SectionItem>({
    initialValues: localData,
    enableReinitialize: true,
    validationSchema: occasionCardsItemSchema,
    onSubmit: (values) => {
      onChange(values);
    }
  });

  const handleLocalChange = (field: string, value: any) => {
    formik.setFieldValue(field, value);
  };

  const handleSave = () => {
    formik.handleSubmit();
  };

  return (
    <MainCard>
      <Grid container spacing={2}>
        <Grid item xs={12}>
          {/* Show existing image preview */}
          {data.previewUrl && !fileMap[data.id ?? ''] && (
            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle2" sx={{ mb: 1 }}>
                Current Image:
              </Typography>
              <Box sx={{ position: 'relative', display: 'inline-block' }}>
                <img
                  src={data.previewUrl}
                  alt="Current image"
                  style={{
                    width: '200px',
                    height: '120px',
                    objectFit: 'fill',
                    borderRadius: '8px',
                    border: '1px solid #e0e0e0'
                  }}
                />
              </Box>
            </Box>
          )}

          <SingleFileUpload
            setFieldValue={(field: string, value: any): void => {
              handleLocalChange('imageUrl', value?.preview ?? '');
              setFileMap({ ...fileMap, [data.id ?? '']: value });
            }}
            accept={{ 'image/*': ['.png', '.jpg', '.jpeg'] }}
            file={fileMap[data.id ?? '']}
          />
        </Grid>
        <Grid item xs={12}>
          <TextField
            fullWidth
            name="title"
            label="Occasion Title"
            value={formik.values.title || ''}
            onChange={(e) => {
              handleLocalChange('title', e.target.value);
            }}
          />
        </Grid>
        <Grid item xs={12}>
          <TextField
            fullWidth
            name="subtitle"
            label="Subtitle (Optional)"
            value={formik.values.subtitle || ''}
            onChange={(e) => {
              handleLocalChange('subtitle', e.target.value);
            }}
          />
        </Grid>
        <Grid item xs={12}>
          <Autocomplete
            loading={isLoading}
            options={facetOptions}
            getOptionLabel={(option) => option.name}
            value={facetOptions.find((c) => c.id === formik.values.entityId) || null}
            onChange={(_, newValue) => {
              handleLocalChange('entityId', newValue?.id || '');
              handleLocalChange('entityType', 'category');
            }}
            onInputChange={(_, value) => setSearchTerm(value)}
            filterOptions={(x) => x} // Disable client-side filtering
            renderInput={(params) => (
              <TextField
                {...params}
                label="Select Category"
                placeholder="Search categories"
                helperText="Type at least 2 characters to search"
              />
            )}
          />
        </Grid>
        <Grid item xs={12}>
          <TextField
            fullWidth
            name="metadata.redirectUrl"
            label="Redirection URL (Optional)"
            value={(formik.values.metadata as OccasionCardsMetadata)?.viewAllLink || ''}
            onChange={(e) => {
              handleLocalChange('metadata', {
                ...formik.values.metadata,
                viewAllLink: e.target.value
              });
            }}
          />
        </Grid>
        <Grid item xs={12}>
          <Box display="flex" justifyContent="flex-end">
            <Button variant="contained" color="primary" startIcon={<Save2 />} onClick={handleSave}>
              Save
            </Button>
          </Box>
        </Grid>
      </Grid>
    </MainCard>
  );
};
