import React, { <PERSON><PERSON><PERSON>, <PERSON>, SetStateAction, useEffect, useState, useMemo } from 'react';
import { Grid, TextField, Button, Box, Autocomplete, Typography } from '@mui/material';
import MainCard from 'components/MainCard';
import { useFormik } from 'formik';
import { SectionItem } from 'types/cms';
import { featuredCollectionItemSchema } from '../../../validations/cms';
import SingleFileUpload from 'components/third-party/dropzone/SingleFileUpload';
import { Save2 as Save } from 'iconsax-react';
import { useSearchCachedCollections, useCachedItemsByIds } from 'hooks/useReduxCachedData';

interface Props {
  data: SectionItem;
  onChange: (value: SectionItem) => void;
  setFileMap: Dispatch<SetStateAction<Record<string, File>>>;
  fileMap: Record<string, File>;
}

export const FeaturedCollectionItem: FC<Props> = React.memo(({ data, onChange, setFileMap, fileMap }) => {
  const [localData, setLocalData] = useState<SectionItem>(data);
  const [searchTerm, setSearchTerm] = useState('');
  const [localFileMap, setLocalFileMap] = useState<Record<string, File>>({ ...fileMap });
  const [isSaved, setIsSaved] = useState(true);

  // Use cached data instead of API calls
  const searchResults = useSearchCachedCollections(searchTerm);
  const selectedCollection = useCachedItemsByIds(data.entityId ? [data.entityId] : [], 'collections');

  // Combine selected collection with search results
  const collectionOptions = useMemo(() => {
    const selectedIds = selectedCollection.map((c: any) => c.id);

    // If searching, show search results + selected collection if not in search results
    if (searchTerm && searchTerm.length >= 2) {
      const additionalSelected = selectedCollection.filter((c: any) => !searchResults.some((r: any) => r.id === c.id));
      return [...searchResults, ...additionalSelected];
    }

    // If not searching, show selected collection + some additional options
    const additionalOptions = searchResults.filter((c: any) => !selectedIds.includes(c.id)).slice(0, 10);
    return [...selectedCollection, ...additionalOptions];
  }, [selectedCollection, searchResults, searchTerm]);

  // Update local data when props change
  useEffect(() => {
    setLocalData(data);
  }, [data]);

  useEffect(() => {
    setLocalFileMap({ ...fileMap });
  }, [fileMap]);

  // No API calls needed - using cached data

  const formik = useFormik<SectionItem>({
    initialValues: localData,
    enableReinitialize: true,
    validationSchema: featuredCollectionItemSchema,
    onSubmit: (values) => {
      onChange(values);
      setIsSaved(true);
      setFileMap({ ...localFileMap });
    }
  });

  const handleLocalChange = (field: string, value: any) => {
    formik.setFieldValue(field, value);
    setIsSaved(false);
  };

  const handleSave = () => {
    formik.handleSubmit();
  };

  return (
    <MainCard>
      <Grid container spacing={2}>
        <Grid item xs={12}>
          {/* Show existing image preview */}
          {data.previewUrl && !localFileMap[data.id ?? ''] && (
            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle2" sx={{ mb: 1 }}>
                Current Image:
              </Typography>
              <Box sx={{ position: 'relative', display: 'inline-block' }}>
                <img
                  src={data.previewUrl}
                  alt="Current image"
                  style={{
                    width: '200px',
                    height: '120px',
                    objectFit: 'fill',
                    borderRadius: '8px',
                    border: '1px solid #e0e0e0'
                  }}
                />
              </Box>
            </Box>
          )}

          <SingleFileUpload
            setFieldValue={(_: string, value: any): void => {
              handleLocalChange('imageUrl', value?.preview ?? '');
              setLocalFileMap({ ...fileMap, [data.id ?? '']: value });
            }}
            accept={{ 'image/*': ['.png', '.jpg', '.jpeg'] }}
            file={localFileMap[data.id ?? '']}
          />
        </Grid>
        <Grid item xs={12}>
          <TextField
            fullWidth
            name="title"
            label="Collection Title"
            value={formik.values.title || ''}
            onChange={(e) => {
              handleLocalChange('title', e.target.value);
            }}
          />
        </Grid>
        <Grid item xs={12}>
          <TextField
            fullWidth
            name="subtitle"
            label="Subtitle (Optional)"
            value={formik.values.subtitle || ''}
            onChange={(e) => {
              handleLocalChange('subtitle', e.target.value);
            }}
          />
        </Grid>
        <Grid item xs={12}>
          <Autocomplete
            loading={false} // No loading since we're using cached data
            options={collectionOptions}
            getOptionLabel={(option) => option.name}
            value={collectionOptions.find((c) => c.id === formik.values.entityId) || null}
            onChange={(_, newValue) => {
              handleLocalChange('entityId', newValue?.id || '');
              handleLocalChange('entityType', 'collection');
            }}
            onInputChange={(_, value) => setSearchTerm(value)}
            filterOptions={(x) => x} // Disable client-side filtering
            renderInput={(params) => (
              <TextField
                {...params}
                label="Select Collection"
                placeholder="Search collections"
                helperText="Type at least 2 characters to search"
              />
            )}
          />
        </Grid>
        <Grid item xs={12}>
          <Box display="flex" justifyContent="flex-end">
            <Button
              variant="contained"
              color="primary"
              startIcon={<Save />}
              onClick={handleSave}
              disabled={isSaved || !formik.dirty || !formik.isValid}
            >
              {isSaved ? 'Saved' : 'Save'}
            </Button>
          </Box>
        </Grid>
      </Grid>
    </MainCard>
  );
});
