import React, { Di<PERSON><PERSON>, FC, SetStateAction, useEffect, useState, useMemo } from 'react';
import { Grid, TextField, Button, Box, Autocomplete, Typography } from '@mui/material';
import MainCard from 'components/MainCard';
import { useFormik } from 'formik';
import { SectionItem } from 'types/cms';
import SingleFileUpload from 'components/third-party/dropzone/SingleFileUpload';
import { Save2 as Save } from 'iconsax-react';
import { facetItemSchema } from '../../../validations/cms';
import { useSearchCachedFacets, useCachedItemsByIds } from 'hooks/useReduxCachedData';

interface Props {
  data: SectionItem;
  onChange: (value: SectionItem) => void;
  setFileMap: Dispatch<SetStateAction<Record<string, File>>>;
  fileMap: Record<string, File>;
}

export const FacetItem: FC<Props> = React.memo(({ data, onChange, setFileMap, fileMap }) => {
  const [localFileMap, setLocalFileMap] = useState<Record<string, File>>({ ...fileMap });
  const [isSaved, setIsSaved] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');

  // Use cached data instead of API calls
  const searchResults = useSearchCachedFacets(searchTerm);
  const facetValueIds = data.metadata?.facetValueIds ?? [];
  const selectedFacetValues = useCachedItemsByIds(facetValueIds, 'facetValues');

  // Combine selected facet values with search results
  const facetOptions = useMemo(() => {
    const selectedIds = selectedFacetValues.map((fv: any) => fv.id);

    // If searching, show search results + selected facet values not in search results
    if (searchTerm && searchTerm.length >= 2) {
      const additionalSelected = selectedFacetValues.filter((fv: any) => !searchResults.some((r: any) => r.id === fv.id));
      return [...searchResults, ...additionalSelected];
    }

    // If not searching, show selected facet values + some additional options
    const additionalOptions = searchResults.filter((fv: any) => !selectedIds.includes(fv.id)).slice(0, 10);
    return [...selectedFacetValues, ...additionalOptions];
  }, [selectedFacetValues, searchResults, searchTerm]);

  const formik = useFormik<SectionItem>({
    initialValues: data,
    enableReinitialize: true,
    validationSchema: facetItemSchema,
    onSubmit: (values) => {
      onChange(values);
      setIsSaved(true);
      setFileMap({ ...localFileMap });
    }
  });

  const handleLocalChange = (field: string, value: any) => {
    formik.setFieldValue(field, value);
    setIsSaved(false);
  };

  const handleSave = () => {
    formik.handleSubmit();
  };

  // No API calls needed - using cached data

  useEffect(() => {
    if (Object.keys(formik.errors).length > 0) {
      // Formik validation errors exist
    }
  }, [formik.errors]);

  return (
    <MainCard>
      <Grid container spacing={2}>
        <Grid item xs={12}>
          {/* Show existing image preview */}
          {data.previewUrl && !localFileMap[data.id ?? ''] && (
            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle2" sx={{ mb: 1 }}>
                Current Image:
              </Typography>
              <Box sx={{ position: 'relative', display: 'inline-block' }}>
                <img
                  src={data.previewUrl}
                  alt="Current image"
                  style={{
                    width: '200px',
                    height: '120px',
                    objectFit: 'fill',
                    borderRadius: '8px',
                    border: '1px solid #e0e0e0'
                  }}
                />
              </Box>
            </Box>
          )}

          <SingleFileUpload
            setFieldValue={(_: string, value: any): void => {
              handleLocalChange('imageUrl', value?.preview ?? '');
              setLocalFileMap({ ...fileMap, [data.id ?? '']: value });
            }}
            accept={{ 'image/*': ['.png', '.jpg', '.jpeg'] }}
            file={localFileMap[data.id ?? '']}
          />
        </Grid>

        <Grid item xs={12}>
          <TextField
            fullWidth
            name="title"
            label="Title"
            value={formik.values.title || ''}
            onChange={(e) => handleLocalChange('title', e.target.value)}
          />
        </Grid>

        <Grid item xs={12}>
          <TextField
            fullWidth
            name="subtitle"
            label="Subtitle (Optional)"
            value={formik.values.subtitle || ''}
            onChange={(e) => handleLocalChange('subtitle', e.target.value)}
          />
        </Grid>

        <Grid item xs={12}>
          <Autocomplete
            multiple
            loading={false} // No loading since we're using cached data
            options={facetOptions}
            getOptionLabel={(option) => option.name ?? ''}
            filterOptions={(x) => x} // disables local filtering since you're using server-side
            value={(formik.values.metadata?.facetValueIds || []).map(
              (id) => facetOptions.find((opt) => opt.id === id) || { id, name: 'Loading...' }
            )}
            onChange={(_, newValues) => {
              const selectedIds = newValues.map((val) => val.id);
              handleLocalChange('metadata', {
                ...formik.values.metadata,
                facetValueIds: selectedIds
              });
              handleLocalChange('entityType', 'facet-values');
            }}
            onInputChange={(_, newInputValue, reason) => {
              if (reason === 'input') {
                setSearchTerm(newInputValue);
              }
            }}
            inputValue={searchTerm}
            renderInput={(params) => (
              <TextField
                {...params}
                label="Select Facet Values"
                placeholder="Search facet values"
                helperText="You can select multiple facet values"
              />
            )}
          />
        </Grid>

        <Grid item xs={12}>
          <Box display="flex" justifyContent="flex-end">
            <Button
              variant="contained"
              color="primary"
              startIcon={<Save />}
              onClick={handleSave}
              disabled={isSaved || !formik.dirty || !formik.isValid}
            >
              {isSaved ? 'Saved' : 'Save'}
            </Button>
          </Box>
        </Grid>
      </Grid>
    </MainCard>
  );
});
