import { <PERSON><PERSON>, TextField, Button, Box, Typography } from '@mui/material';
import MainCard from 'components/MainCard';
import { useFormik } from 'formik';
import { Dispatch, FC, SetStateAction, useEffect, useState } from 'react';
import { CategoryGridMetadata, SectionItem } from 'types/cms';
import { categoryGridItemSchema } from '../../../validations/cms';
import SingleFileUpload from 'components/third-party/dropzone/SingleFileUpload';
import { Save2 } from 'iconsax-react';

interface Props {
  data: SectionItem;
  onChange: (value: SectionItem) => void;
  setFileMap: Dispatch<SetStateAction<Record<string, File>>>;
  fileMap: Record<string, File>;
}

export const CategoryGridItem: FC<Props> = ({ data, onChange, setFileMap, fileMap }) => {
  const [localData, setLocalData] = useState<SectionItem>(data);

  // Update local data when props change
  useEffect(() => {
    setLocalData(data);
  }, [data]);

  const formik = useFormik<SectionItem>({
    initialValues: localData,
    enableReinitialize: true,
    validationSchema: categoryGridItemSchema,
    onSubmit: (values) => {
      onChange(values);
    }
  });

  const handleLocalChange = (field: string, value: any) => {
    formik.setFieldValue(field, value);
  };

  const handleSave = () => {
    formik.handleSubmit();
  };

  return (
    <MainCard>
      <Grid container spacing={2}>
        <Grid item xs={12}>
          {/* Show existing image preview */}
          {data.previewUrl && !fileMap[data.id ?? ''] && (
            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle2" sx={{ mb: 1 }}>
                Current Image:
              </Typography>
              <Box sx={{ position: 'relative', display: 'inline-block' }}>
                <img
                  src={data.previewUrl}
                  alt="Current image"
                  style={{
                    width: '200px',
                    height: '120px',
                    objectFit: 'fill',
                    borderRadius: '8px',
                    border: '1px solid #e0e0e0'
                  }}
                />
              </Box>
            </Box>
          )}

          <SingleFileUpload
            setFieldValue={(field: string, value: any): void => {
              handleLocalChange('imageUrl', value?.preview ?? '');
              setFileMap({ ...fileMap, [data.id ?? '']: value });
            }}
            accept={{ 'image/*': ['.png', '.jpg', '.jpeg'] }}
            file={fileMap[data.id ?? '']}
          />
        </Grid>
        <Grid item xs={12}>
          <TextField
            fullWidth
            name="title"
            label="Category Name"
            value={formik.values.title || ''}
            onChange={(e) => {
              handleLocalChange('title', e.target.value);
            }}
          />
        </Grid>
        <Grid item xs={12}>
          <TextField
            fullWidth
            name="metadata.categoryId"
            label="Category ID"
            value={(formik.values.metadata as CategoryGridMetadata)?.categoryId || ''}
            onChange={(e) => {
              handleLocalChange('metadata', {
                ...formik.values.metadata,
                categoryId: e.target.value
              });
            }}
          />
        </Grid>
        <Grid item xs={12}>
          <Box display="flex" justifyContent="flex-end">
            <Button variant="contained" color="primary" startIcon={<Save2 />} onClick={handleSave}>
              Save
            </Button>
          </Box>
        </Grid>
      </Grid>
    </MainCard>
  );
};
